<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"  xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" th:href="@{/images/logo-solo.png}">

    <title>Log in </title>

    <!-- Vendors Style-->
    <link rel="stylesheet" th:href="@{/css/vendors_css.css}">

    <!-- Style-->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/skin_color.css}">

</head>

<body class="hold-transition theme-primary bg-img"
      th:style="|background-image: url('@{/images/auth-bg/background.png}')|">

<div class="container h-p100">
    <div class="row align-items-center justify-content-md-center h-p100">

        <div class="col-12">
            <div class="row justify-content-center g-0">
                <div class="col-lg-5 col-md-5 col-12">
                    <div class="bg-white rounded10 shadow-lg">
                        <div class="content-top-agile p-20 pb-0">
                            <h2 class="text-primary">Iniciar sesión</h2>
                            <div><img th:src="@{/images/logo-sanMiguel.png}" alt="Logo Municipalidad"  width="150"></div>
                            <br>
                            <p class="mb-0">Ingresa tus credenciales para abrir sesión </p>
                        </div>
                        <div class="p-30">
                            <form th:action="@{/procesar-login}" method="post">
                                <div class="form-group">
                                    <div class="input-group mb-3">
                                        <span class="input-group-text bg-transparent"><i class="ti-user"></i></span>
                                        <input type="text" class="form-control ps-15 bg-transparent" name="username" placeholder="Correo electrónico">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="input-group mb-3">
                                        <span class="input-group-text  bg-transparent"><i class="ti-lock"></i></span>
                                        <input type="password" class="form-control ps-15 bg-transparent" name="password" placeholder="Contraseña">
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 10px;">
                                    <div class="col-6">
                                        <div class="checkbox">
                                            <input type="checkbox" id="basic_checkbox_1" >
                                            <label for="basic_checkbox_1">Recuérdame</label>
                                        </div>
                                    </div>
                                    <!-- /.col -->
                                    <div class="col-6">
                                        <div class="fog-pwd text-end">
                                            <a th:href="@{/login/recoverpassword}" class="hover-warning"><i class="ion ion-locked"></i> ¿Olvido la contraseña?</a><br>
                                        </div>
                                    </div>
                                    <!-- /.col -->
                                    <div class="col-12 text-center">
                                        <button type="submit" class="btn btn-danger mt-10">Ingresar</button>
                                    </div>
                                    <!-- /.col -->
                                </div>
                            </form>
                            <div class="text-center">
                                <p class="mt-15 mb-0">¿Aún no tiene una cuenta? <a th:href="@{/signup}" class="text-warning ms-5">Registrese</a></p>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <p class="mt-20 text-black">- Conoce más de nosotros en -</p>
                        <p class="gap-items-2 mb-20">
                            <a class="btn btn-social-icon btn-round btn-facebook" href="https://www.facebook.com/municipalidadsanmiguel/?locale=es_LA" target="_blank"><i class="fa fa-facebook"></i></a>
                            <a class="btn btn-social-icon btn-round btn-twitter" href="https://x.com/munisanmiguelpe?lang=es" target="_blank"><i class="fa fa-twitter"></i></a>
                            <a class="btn btn-social-icon btn-round btn-instagram" href="https://www.instagram.com/municipalidadsanmiguel/?hl=es" target="_blank"><i class="fa fa-instagram"></i></a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Vendor JS -->
<script  th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script th:if="${msg != null}" th:inline="javascript">
    Swal.fire({
        icon: 'success',
        title: 'Éxito',
        text: [[${msg}]],
        confirmButtonText: 'OK'
    });
</script>

</body>
</html>