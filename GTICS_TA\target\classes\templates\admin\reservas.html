<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" th:href="@{/images/san_miguel_logo.ico}">

    <title>Admin - Reservas</title>

    <!-- CSRF Token -->
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>

    <!-- Vendors Style-->
    <link rel="stylesheet" th:href="@{/css/vendors_css.css}">

    <!-- Style-->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/skin_color.css}">






    <style>

        .dataTables_filter {
            margin-bottom: 20px; /* Separación */
            float: right; /* Puedes quitar esto si lo quieres alineado a la izquierda */
        }

        .filter-btn.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn-group .btn {
            margin-right: 2px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        /* Estilos para botones de acciones */
        .d-flex.flex-column.gap-1 {
            gap: 0.25rem !important;
            min-width: 80px;
        }

        .d-flex.flex-column.gap-1 .btn {
            font-size: 0.75rem;
            padding: 0.4rem;
            white-space: nowrap;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        /* Botón ojo con fondo suave */
        .btn-info-soft {
            background-color: rgba(125, 214, 231, 0.1) !important;
            border: none !important;
            color: #0dcaf0 !important;
        }

        .btn-info-soft:hover {
            background-color: rgba(13, 202, 240, 0.356) !important;
            color: #0dcaf0 !important;
        }

        /* Botones con colores pastel */
        .btn-success-pastel {
            background-color: #aaf0baa6 !important;
            color: #31c754 !important;
        }

        .btn-danger-pastel {
            background-color: #eed1d380 !important;
            color: #e44b5a !important;
        }

        /* Botones deshabilitados */
        .btn.opacity-50 {
            opacity: 0.5 !important;
            pointer-events: none;
        }

        .btn.opacity-50:hover {
            transform: none !important;
        }

        /* Filtros de tipo de espacio */
        .filter-btn.active {
            background-color: #1DBFC1 !important;
            border-color: #1DBFC1 !important;
            color: white !important;
        }

        .filter-btn {
            border-color: #1DBFC1;
            color: #1DBFC1;
        }

        .filter-btn:hover {
            background-color: #1DBFC1;
            border-color: #1DBFC1;
            color: white;
        }

        /* Texto más pequeño para horario y fecha */
        .small-text {
            font-size: 0.85rem;
        }

        /* Estado en dos líneas */
        .estado-dos-lineas {
            line-height: 1.2;
            text-align: center;
        }

        /* Título de tabla */
        .table-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }
    </style>

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">
        <div class="d-flex align-items-center logo-box justify-content-center">
            <!-- Logo -->
            <a th:href="@{/admin}" class="logo">
                <!-- logo-->
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
                </div>
            </a>
        </div>
        <!-- Header Navbar -->
        <nav class="navbar navbar-static-top">
            <!-- Sidebar toggle button-->
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>

                </ul>
            </div>

            <div th:replace="fragments :: navbarAdmin"></div>
        </nav>
    </header>

    <aside th:replace="fragments :: sideBarAdmin"></aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h2 class="fw-bold mb-0 me-3">Reservas</h2>

                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <!-- Botones de filtro por tipo de espacio -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                            <button type="button" class="btn btn-outline-primary filter-btn active" data-tipo="all">
                                Todos
                            </button>
                            <button type="button" class="btn btn-outline-primary filter-btn" th:each="tipo : ${tiposEspacio}"
                                    th:data-tipo="${tipo.id}" th:text="${tipo.nombre}">
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-12">
                        <div class="box">
                            <div class="box-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped" id="tablaReservas">
                                        <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Registro</th>
                                            <th>Datos Usuario</th>
                                            <th>Datos Servicio</th>
                                            <th>Fecha Reserva</th>
                                            <th>Horario Reservado</th>
                                            <th>Datos Pago</th>
                                            <th>Estado</th>
                                            <th>Acciones</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:if="${listaReservas != null and !listaReservas.isEmpty()}"
                                            th:each="reserva : ${listaReservas}"
                                            th:data-tipo="${reserva.espacioDeportivo?.tipoEspacio?.id}">
                                            <td th:text="${reserva.id}"></td>
                                            <td>
                                                <div th:if="${reserva.fechaRegistro != null}" class="d-flex flex-column">
                                                    <small th:text="${#dates.format(reserva.fechaRegistro, 'dd/MM/yyyy')}"></small>
                                                    <small th:text="${#dates.format(reserva.fechaRegistro, 'hh:mm a')}" class="text-muted"></small>
                                                </div>
                                                <small th:if="${reserva.fechaRegistro == null}" class="text-muted">N/A</small>
                                            </td>
                                            <td>
                                                <div th:if="${reserva.usuario != null}">
                                                    <strong th:text="${reserva.usuario.nombres + ' ' + reserva.usuario.apellidos}"></strong><br>
                                                    <small class="text-muted" th:text="${reserva.usuario.correo}"></small><br>
                                                    <small class="text-muted" th:text="'DNI: ' + ${reserva.usuario.dni}"></small>
                                                </div>
                                                <span th:if="${reserva.usuario == null}" class="text-muted">N/A</span>
                                            </td>
                                            <td>
                                                <div th:if="${reserva.espacioDeportivo != null}">
                                                    <strong th:text="${reserva.espacioDeportivo.nombre}"></strong><br>
                                                    <small class="text-muted" th:text="${reserva.espacioDeportivo.tipoEspacio?.nombre}"></small>
                                                </div>
                                                <span th:if="${reserva.espacioDeportivo == null}" class="text-muted">N/A</span>
                                            </td>
                                            <td class="text-center" th:text="${reserva.fechaReserva}"></td>
                                            <td class="text-center">
                                                <div th:if="${reserva.horario != null}">
                                                    <div th:text="${reserva.horario.horaInicio}"></div>
                                                    <div th:text="${reserva.horario.horaFin}"></div>
                                                </div>
                                                <span th:if="${reserva.horario == null}" class="text-muted">N/A</span>
                                            </td>

                                            <td>
                                                <div th:if="${reserva.pago != null}">
                                                    <span class="fw-bold" th:text="'S/ ' + ${reserva.pago.cantidad}"></span><br>
                                                    <small th:text="${reserva.pago.medioPago?.nombre}" class="text-muted"></small><br>
                                                    <span th:if="${reserva.pago.estadoPago != null}" th:switch="${reserva.pago.estadoPago.name()}">
                                                        <span th:case="'PENDIENTE'" class="badge bg-warning">Pendiente</span>
                                                        <span th:case="'APROBADO'" class="badge bg-success">Aprobado</span>
                                                        <span th:case="'RECHAZADO'" class="badge bg-danger">Rechazado</span>
                                                        <span th:case="*" class="badge bg-secondary">Desconocido</span>
                                                    </span>
                                                    <span th:if="${reserva.pago.estadoPago == null}" class="badge bg-secondary">Sin estado</span>
                                                </div>
                                                <span th:if="${reserva.pago == null}" class="text-muted">N/A</span>
                                            </td>
                                            <td class="text-center">
                                                <span th:if="${reserva.estadoReserva != null}" th:switch="${reserva.estadoReserva.name()}">
                                                    <span th:case="'ACTIVA'" class="badge bg-success">Activa</span>
                                                    <span th:case="'CANCELADA_USUARIO'" class="badge bg-warning">Cancelada por Usuario</span>
                                                    <span th:case="'CANCELADA_MANTENIMIENTO'" class="badge bg-info">Cancelada por Mantenimiento</span>
                                                    <span th:case="'CANCELADA_ADMIN'" class="badge bg-danger estado-dos-lineas">
                                                        Cancelada por<br>
                                                        <span th:if="${reserva.canceladoPor != null}" th:text="${reserva.canceladoPor.nombres}"></span>
                                                        <span th:if="${reserva.canceladoPor == null}">Admin</span>
                                                    </span>
                                                    <span th:case="'COMPLETADA'" class="badge bg-primary">Completada</span>
                                                    <span th:case="*" class="badge bg-secondary">Desconocido</span>
                                                </span>
                                                <span th:if="${reserva.estadoReserva == null}" class="badge bg-secondary">Sin estado</span>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column gap-1">
                                                    <!-- Botón Ver Detalles - Siempre habilitado -->
                                                    <button type="button" class="btn btn-sm btn-info-soft"
                                                            th:onclick="|verDetallesPago(${reserva.pago?.id ?: 'null'})|"
                                                            title="Ver detalles del pago">
                                                        <i data-feather="eye"></i>
                                                    </button>

                                                    <!-- Botón Aprobar -->
                                                    <button type="button"
                                                            th:if="${reserva.pago != null}"
                                                            th:class="${(reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() == 'APROBADO') or (reserva.estadoReserva != null and reserva.estadoReserva.name() == 'COMPLETADA')} ? 'btn btn-sm btn-secondary opacity-50' : 'btn btn-sm btn-success-pastel'"
                                                            th:onclick="${(reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() != 'APROBADO') and (reserva.estadoReserva == null or reserva.estadoReserva.name() != 'COMPLETADA')} ? 'aprobarPago(' + ${reserva.pago.id} + ')' : 'return false;'"
                                                            th:title="${reserva.estadoReserva != null and reserva.estadoReserva.name() == 'COMPLETADA'} ? 'Reserva completada' : (${reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() == 'APROBADO'} ? 'Pago ya aprobado' : 'Aprobar pago')"
                                                            th:style="${(reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() == 'APROBADO') or (reserva.estadoReserva != null and reserva.estadoReserva.name() == 'COMPLETADA')} ? 'cursor: not-allowed;' : ''">
                                                        <i data-feather="check"></i>
                                                    </button>

                                                    <!-- Botón Rechazar/Cancelar -->
                                                    <button type="button"
                                                            th:if="${reserva.pago != null}"
                                                            th:class="${(reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() == 'RECHAZADO') or (reserva.estadoReserva != null and reserva.estadoReserva.name() == 'COMPLETADA')} ? 'btn btn-sm btn-secondary opacity-50' : 'btn btn-sm btn-danger-pastel'"
                                                            th:onclick="${(reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() != 'RECHAZADO') and (reserva.estadoReserva == null or reserva.estadoReserva.name() != 'COMPLETADA')} ? 'rechazarPago(' + ${reserva.pago.id} + ')' : 'return false;'"
                                                            th:title="${reserva.estadoReserva != null and reserva.estadoReserva.name() == 'COMPLETADA'} ? 'Reserva completada' : (${reserva.pago.medioPago != null and reserva.pago.medioPago.nombre == 'Tarjeta de Crédito/Débito'} ? 'Cancelar reserva' : (${reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() == 'RECHAZADO'} ? 'Pago ya rechazado' : 'Rechazar pago'))"
                                                            th:style="${(reserva.pago.estadoPago != null and reserva.pago.estadoPago.name() == 'RECHAZADO') or (reserva.estadoReserva != null and reserva.estadoReserva.name() == 'COMPLETADA')} ? 'cursor: not-allowed;' : ''">
                                                        <i data-feather="x"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr th:if="${listaReservas == null or listaReservas.isEmpty()}">
                                            <td colspan="9" class="text-center">No hay reservas disponibles</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- /.content -->
        </div>
    </div>
    <!-- /.content-wrapper -->
    <footer class="main-footer">
        <div class="pull-right d-none d-sm-inline-block">
            <ul class="nav nav-primary nav-dotted nav-dot-separated justify-content-center justify-content-md-end">
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0)">Contáctanos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">FAQ</a>
                </li>
            </ul>
        </div>
        &copy; <script>document.write(new Date().getFullYear())</script> <a href="https://www.munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
    </footer>

    <!-- Modal de Detalles de Pago -->
    <div class="modal fade" id="modalDetallesPago" tabindex="-1" aria-labelledby="modalDetallesPagoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="modalDetallesPagoLabel">Detalles del Pago</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body" id="detallesPagoContent">
                    <!-- El contenido se cargará dinámicamente -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Rechazo de Pago -->
    <div class="modal fade" id="modalRechazarPago" tabindex="-1" aria-labelledby="modalRechazarPagoLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="formRechazarPago">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="modalRechazarPagoLabel">Rechazar Pago</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="rechazarPagoId">
                        <div class="mb-3">
                            <label for="motivoRechazo" class="form-label">Motivo del rechazo <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="motivoRechazo" rows="3"
                                      placeholder="Explique el motivo del rechazo del pago..." required></textarea>
                        </div>
                        <div class="alert alert-danger d-none" id="rechazarPagoError"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">Rechazar Pago</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- /.control-sidebar -->

    <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
    <div class="control-sidebar-bg"></div>

</div>
<!-- ./wrapper -->

<!-- ./side demo panel -->

<!-- Sidebar -->



<!-- Page Content overlay -->
<!-- Script de DataTables si no está activado -->
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

<!-- Inicialización de DataTables -->

<script>
    // Variable global para almacenar la instancia de DataTable
    let tablaReservas;

    // Funcionalidad de filtros por tipo de espacio
    $(document).ready(function() {
        // Colapsar sidebar por defecto en la página de reservas
        if (!$('body').hasClass('sidebar-collapse')) {
            $('body').addClass('sidebar-collapse');
        }

        // Inicializar DataTable primero
        tablaReservas = $('#tablaReservas').DataTable({
            pageLength: 10,
            lengthChange: false,
            info: true,
            paging: true,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json",
                search: "Buscar:",
                paginate: {
                    previous: "Anterior",
                    next: "Siguiente"
                },
                info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                infoEmpty: "Mostrando 0 a 0 de 0 registros",
                infoFiltered: "(filtrado de _MAX_ registros totales)"
            },
            dom: '<"d-flex justify-content-between align-items-center mb-3"<"table-title">f>rt<"d-flex justify-content-between align-items-center mt-3"ip><"clear">',
            columnDefs: [
                { orderable: false, targets: [8] } // Deshabilitar ordenamiento en columna de acciones
            ]
        });

        // Función de filtro personalizada para DataTables
        $.fn.dataTable.ext.search.push(
            function(settings, data, dataIndex) {
                // Solo aplicar a nuestra tabla
                if (settings.nTable.id !== 'tablaReservas') {
                    return true;
                }

                // Obtener el tipo seleccionado
                const tipoSeleccionado = $('.filter-btn.active').data('tipo');

                // Si es "all", mostrar todas las filas
                if (tipoSeleccionado === 'all') {
                    return true;
                }

                // Obtener el tipo de la fila actual
                const row = tablaReservas.row(dataIndex).node();
                const tipoFila = $(row).data('tipo');

                // Retornar true si coincide con el filtro
                return tipoFila == tipoSeleccionado;
            }
        );

        // Manejar clicks en botones de filtro
        $('.filter-btn').on('click', function() {
            // Actualizar estado visual de botones
            $('.filter-btn').removeClass('active');
            $(this).addClass('active');

            // Redibujar la tabla para aplicar el filtro
            tablaReservas.draw();
        });
    });

    // Función para ver detalles del pago
    function verDetallesPago(pagoId) {
        if (!pagoId) {
            alert('No hay información de pago disponible');
            return;
        }

        // Llamada AJAX para obtener los detalles del pago
        $.ajax({
            url: '/admin/reservas/detalles-pago/' + pagoId,
            type: 'GET',
            success: function(data) {
                let estadoBadge = '';
                switch(data.estadoPago) {
                    case 'PENDIENTE':
                        estadoBadge = '<span class="badge bg-warning">Pendiente</span>';
                        break;
                    case 'APROBADO':
                        estadoBadge = '<span class="badge bg-success">Aprobado</span>';
                        break;
                    case 'RECHAZADO':
                        estadoBadge = '<span class="badge bg-danger">Rechazado</span>';
                        break;
                    default:
                        estadoBadge = '<span class="badge bg-secondary">Desconocido</span>';
                }

                let contenido = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Información del Pago</h6>
                            <p><strong>ID de Pago:</strong> ${data.id}</p>
                            <p><strong>Cantidad:</strong> S/ ${data.cantidad}</p>
                            <p><strong>Estado:</strong> ${estadoBadge}</p>
                            <p><strong>Fecha:</strong> ${new Date(data.fechaPago).toLocaleString()}</p>
                            <p><strong>Método:</strong> ${data.medioPago}</p>
                            ${data.numeroTransaccion ? '<p><strong>Nº Transacción:</strong> ' + data.numeroTransaccion + '</p>' : ''}
                `;

                if (data.verificadoPor) {
                    contenido += `
                        <hr>
                        <h6>Información de Verificación</h6>
                        <p><strong>Verificado por:</strong> ${data.verificadoPor}</p>
                        <p><strong>Fecha verificación:</strong> ${new Date(data.fechaVerificacion).toLocaleString()}</p>
                    `;
                }

                if (data.observacionesAdmin) {
                    contenido += `<p><strong>Observaciones:</strong> ${data.observacionesAdmin}</p>`;
                }

                contenido += '</div><div class="col-md-6">';

                if (data.tipoPago === 'MANUAL') {
                    contenido += `
                        <h6>Datos de Transferencia</h6>
                        ${data.datosCuenta ? '<pre>' + data.datosCuenta + '</pre>' : '<p>No hay datos de cuenta disponibles</p>'}
                        <hr>
                        <h6>Comprobante</h6>
                    `;

                    if (data.tieneComprobantes) {
                        contenido += `
                            <div class="text-center">
                                <p class="text-info">📄 Comprobante disponible</p>
                                <small>El usuario ha subido comprobante de pago</small>
                            </div>
                        `;
                    } else {
                        contenido += '<p class="text-muted">No hay comprobante disponible</p>';
                    }
                } else {
                    contenido += `
                        <h6>Pago con Tarjeta</h6>
                        <p class="text-success">✅ Pago procesado automáticamente</p>
                        <small class="text-muted">Los pagos con tarjeta se procesan automáticamente a través de la pasarela de pagos.</small>
                    `;
                }

                contenido += '</div></div>';

                $('#detallesPagoContent').html(contenido);
                $('#modalDetallesPago').modal('show');
            },
            error: function(xhr, status, error) {
                alert('Error al cargar los detalles del pago: ' + (xhr.responseText || error));
            }
        });
    }

    // Función para aprobar pago
    function aprobarPago(pagoId) {
        if (!pagoId) {
            alert('ID de pago no válido');
            return;
        }

        if (confirm('¿Está seguro de que desea aprobar este pago?')) {
            $.ajax({
                url: '/admin/reservas/aprobar-pago/' + pagoId,
                type: 'POST',
                beforeSend: function(xhr) {
                    // Agregar token CSRF
                    var token = $("meta[name='_csrf']").attr("content");
                    var header = $("meta[name='_csrf_header']").attr("content");
                    if (token && header) {
                        xhr.setRequestHeader(header, token);
                    }
                },
                success: function(response) {
                    alert('Pago aprobado exitosamente');
                    location.reload(); // Recargar la página para ver los cambios
                },
                error: function(xhr, status, error) {
                    alert('Error al aprobar el pago: ' + (xhr.responseText || error));
                }
            });
        }
    }

    // Función para rechazar pago
    function rechazarPago(pagoId) {
        if (!pagoId) {
            alert('ID de pago no válido');
            return;
        }

        $('#rechazarPagoId').val(pagoId);
        $('#motivoRechazo').val('');
        $('#rechazarPagoError').addClass('d-none');
        $('#modalRechazarPago').modal('show');
    }

    // Manejar envío del formulario de rechazo
    $('#formRechazarPago').on('submit', function(e) {
        e.preventDefault();

        const pagoId = $('#rechazarPagoId').val();
        const motivo = $('#motivoRechazo').val().trim();

        if (!motivo) {
            $('#rechazarPagoError').text('El motivo del rechazo es obligatorio').removeClass('d-none');
            return;
        }

        $.ajax({
            url: '/admin/reservas/rechazar-pago/' + pagoId,
            type: 'POST',
            data: { motivo: motivo },
            beforeSend: function(xhr) {
                // Agregar token CSRF
                var token = $("meta[name='_csrf']").attr("content");
                var header = $("meta[name='_csrf_header']").attr("content");
                if (token && header) {
                    xhr.setRequestHeader(header, token);
                }
            },
            success: function(response) {
                alert('Pago rechazado exitosamente');
                $('#modalRechazarPago').modal('hide');
                location.reload(); // Recargar la página para ver los cambios
            },
            error: function(xhr, status, error) {
                $('#rechazarPagoError').text('Error al rechazar el pago: ' + (xhr.responseText || error)).removeClass('d-none');
            }
        });
    });
</script>



<script>
    feather.replace();
</script>





<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>

<script th:src="@{/assets/vendor_components/datatable/datatables.min.js}"></script>

<!-- Rhythm Admin App -->
<script th:src="@{/js/template.js}"></script>
<script th:src="@{/js/pages/patients.js}"></script>

</body>
</html>
