/* 
 * https://github.com/LukyVj/colofilter.css/blob/master/LICENSE
*/

/*
.rs-duotone-kb .tp-bgimg {
  display: none !important;
}

[class^="rs-duotone"] {
  transform: none !important;
}
*/

#divbgholder .slotholder {z-index: 0 !important}
#divbgholder .oldslotholder {display: none !important}
.rs-duotone-simplified .tp-revslider-slidesli {opacity: 1 !important}
.rs-duotone-simplified  .rs-duotone-slide {z-index: 9999 !important; opacity: 0 !important}

[class^="rs-duotone"] {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

[class^="rs-duotone"] .slotholder,
[class^="rs-duotone"] .rs-duotone-thumb {
  mix-blend-mode: luminosity;
  backface-visibility: visible !important;
}

[class^="rs-duotone"]:after {
  display: block;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  -webkit-filter: contrast(1.3);
  filter: contrast(1.3);
  mix-blend-mode: hue;
}

[class^="rs-duotone"][class*="-dark"] .slotholder,
[class^="rs-duotone"][class*="-dark"] .rs-duotone-thumb {
  mix-blend-mode: darken;
}

[class^="rs-duotone"][class*="-dark"]:after {
  mix-blend-mode: lighten !important;
}

[class^="rs-duotone"][class*="-light"] .slotholder,
[class^="rs-duotone"][class*="-light"] .rs-duotone-thumb {
  mix-blend-mode: lighten;
}

[class^="rs-duotone"][class*="-light"]:after {
  mix-blend-mode: darken !important;
}

[class^="rs-duotone"][class*="-red"] {
  background: #E50914 !important;
}

[class^="rs-duotone"][class*="-red"]:after {
  mix-blend-mode: hard-light;
  -webkit-filter: contrast(0.6) saturate(120%) brightness(1.2);
  filter: contrast(0.6) saturate(120%) brightness(1.2);
}

[class^="rs-duotone"][class*="-red"][class*="-dark"]:after {
  mix-blend-mode: lighten !important;
  -webkit-filter: contrast(1.1) !important;
  filter: contrast(1.1) !important;
}

[class^="rs-duotone"][class*="-red"][class*="-light"]:after {
  mix-blend-mode: color-dodge !important;
  -webkit-filter: saturate(400%) contrast(1.5);
  filter: saturate(400%) contrast(1.5);
}

[class^="rs-duotone"][class*="-red"]:after {
  background: #E50914 !important;
}

[class^="rs-duotone"][class*="-red"]:after {
  background: #282581 !important;
}

[class^="rs-duotone"][class*="-orange"] {
  background: #FCA300 !important;
}

[class^="rs-duotone"][class*="-orange"][class*="-dark"]:after {
  mix-blend-mode: darken !important;
}

[class^="rs-duotone"][class*="-orange"][class*="-light"]:after {
  mix-blend-mode: hue !important;
  -webkit-filter: saturate(400%) contrast(1.5);
  filter: saturate(400%) contrast(1.5);
}

[class^="rs-duotone"][class*="-orange"]:after {
  background: #FCA300 !important;
}

[class^="rs-duotone"][class*="-blue"] {
  background: #0066BF !important;
}

[class^="rs-duotone"][class*="-blue"]:not([class*="-dark"]):not([class*="-light"]):after {
  mix-blend-mode: hard-light;
  -webkit-filter: brightness(0.6);
  filter: brightness(0.6);
}

[class^="rs-duotone"][class*="-blue"][class*="-dark"]:after {
  mix-blend-mode: darken !important;
}

[class^="rs-duotone"][class*="-blue"]:after {
  background: #0066BF !important;
}

[class^="rs-duotone"][class*="-blue"]:after {
  background: #93EF90 !important;
}

[class^="rs-duotone"][class*="-yellow"] {
  background: #FEDD31 !important;
}

[class^="rs-duotone"][class*="-yellow"]:not([class*="-dark"]):not([class*="-light"]):after {
  -webkit-filter: brightness(3.5);
  filter: brightness(3.5);
  mix-blend-mode: soft-light;
}

[class^="rs-duotone"][class*="-yellow"][class*="-dark"]:after {
  mix-blend-mode: color-dodge !important;
  -webkit-filter: hue-rotate(70deg);
  filter: hue-rotate(70deg);
}

[class^="rs-duotone"][class*="-yellow"][class*="-light"] {
  background: #000000 !important;
}

[class^="rs-duotone"][class*="-yellow"][class*="-light"]:after {
  mix-blend-mode: color !important;
  -webkit-filter: brightness(3) hue-rotate(93deg) contrast(2) saturate(150);
  filter: brightness(3) hue-rotate(93deg) contrast(2) saturate(150);
}

[class^="rs-duotone"][class*="-yellow"]:after {
  background: #FEDD31 !important;
}

[class^="rs-duotone"][class*="-yellow"]:after {
  background: #EF3CB4 !important;
}

[class^="rs-duotone"][class*="-purple"] {
  background: #BC6D14 !important;
}

[class^="rs-duotone"][class*="-purple"]:not([class*="-dark"]):not([class*="-light"]) {
  background: rebeccapurple !important;
}

[class^="rs-duotone"][class*="-purple"]:not([class*="-dark"]):not([class*="-light"]):after {
  mix-blend-mode: darken !important;
}

[class^="rs-duotone"][class*="-purple"][class*="-dark"] {
  background: #B10AFF !important;
}

[class^="rs-duotone"][class*="-purple"][class*="-dark"]:after {
  mix-blend-mode: soft-light !important;
  -webkit-filter: saturate(100);
  filter: saturate(100);
}

[class^="rs-duotone"][class*="-purple"][class*="-light"]:after {
  background: #A37FC7 !important;
  -webkit-filter: saturate(520%) brightness(10.5) contrast(350) !important;
  filter: saturate(520%) brightness(10.5) contrast(350) !important;
}

[class^="rs-duotone"][class*="-purple"]:after {
  background: #BC6D14 !important;
}

[class^="rs-duotone"][class*="-purple"]:after {
  background: #ACFCEE !important;
}

[class^="rs-duotone"][class*="-green"] {
  background: #11C966 !important;
}

[class^="rs-duotone"][class*="-green"]:not([class*="-dark"]):not([class*="-light"]):after {
  mix-blend-mode: soft-light;
}

[class^="rs-duotone"][class*="-green"][class*="-light"]:after {
  mix-blend-mode: color-dodge !important;
  -webkit-filter: saturate(100%) brightness(0.8) contrast(160%);
  filter: saturate(100%) brightness(0.8) contrast(160%);
}

[class^="rs-duotone"][class*="-green"]:after {
  background: #11C966 !important;
}

[class^="rs-duotone"][class*="-green"]:after {
  background: #2D3181 !important;
}

[class^="rs-duotone"][class*="-pink"] {
  background: #EA4C89 !important;
}

[class^="rs-duotone"][class*="-pink"][class*="-dark"]:after {
  background: #1D0E14 !important;
}

[class^="rs-duotone"][class*="-pink"][class*="-light"]:after {
  background: #FF468D !important;
  mix-blend-mode: lighten !important;
  -webkit-filter: contrast(1) saturate(250%) !important;
  filter: contrast(1) saturate(250%) !important;
}

[class^="rs-duotone"][class*="-pink"]:after {
  background: #EA4C89 !important;
}

[class^="rs-duotone"][class*="-pink"]:after {
  background: #EA4C89 !important;
}

[class^="rs-duotone"][class*="-blue-yellow"]:not([class*="-dark"]):not([class*="-light"]) {
  background: linear-gradient(to top left, #55ACEE, #FEDD31) !important;
}

[class^="rs-duotone"][class*="-blue-yellow"][class*="-dark"]:after {
  mix-blend-mode: hard-light !important;
}

[class^="rs-duotone"][class*="-blue-yellow"][class*="-light"]:after {
  mix-blend-mode: hard-light !important;
  -webkit-filter: none;
  filter: none;
}

[class^="rs-duotone"][class*="-blue-yellow"]:after {
  background: linear-gradient(to top left, #55ACEE, #FEDD31) !important;
}

[class^="rs-duotone"][class*="-pink-yellow"]:not([class*="-dark"]):not([class*="-light"]) {
  background: linear-gradient(to bottom right, #FAA6FB, #FBBC05) !important;
}

[class^="rs-duotone"][class*="-pink-yellow"][class*="-dark"]:after {
  mix-blend-mode: hue !important;
  -webkit-filter: none !important;
  filter: none !important;
}

[class^="rs-duotone"][class*="-pink-yellow"][class*="-light"]:after {
  mix-blend-mode: hard-light !important;
  -webkit-filter: none !important;
  filter: none !important;
}

[class^="rs-duotone"][class*="-pink-yellow"]:after {
  background: linear-gradient(to top left, #FAA6FB, #FBBC05) !important;
}

[class^="rs-duotone"][class*="-red-blue"]:not([class*="-dark"]):not([class*="-light"]) {
  background: linear-gradient(to bottom right, #3993E2, #E2544B) !important;
}

[class^="rs-duotone"][class*="-red-blue"]:not([class*="-dark"]):not([class*="-light"]):after {
  -webkit-filter: none;
  filter: none;
  mix-blend-mode: hard-light;
}

[class^="rs-duotone"][class*="-red-blue"][class*="-dark"]:after {
  mix-blend-mode: hard-light !important;
  -webkit-filter: none !important;
  filter: none !important;
}

[class^="rs-duotone"][class*="-red-blue"][class*="-light"]:after {
  mix-blend-mode: screen !important;
  -webkit-filter: saturate(300%) brightness(1.2) !important;
  filter: saturate(300%) brightness(1.2) !important;
}

[class^="rs-duotone"][class*="-red-blue"]:after {
  background: linear-gradient(to bottom right, #3993E2, #E2544B) !important;
}


