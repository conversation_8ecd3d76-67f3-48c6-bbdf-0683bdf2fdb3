;/*
 2017 ThemePunch
 @version   1.0.0
*/
(function(){window.RsRevealerAddOn=function(p,a,F){function y(){if(!q){switch(r){case "open_horizontal":c+="width: 50%; height: 100%; top: 0; left: 0";t+="width: 50%; height: 100%; top: 0; left: 50%";b.width="0%";u.left="100%";break;case "open_vertical":c+="width: 100%; height: 50%; top: 0; left: 0";t+="width: 100%; height: 50%; top: 50%; left: 0";b.height="0%";u.top="100%";break;case "split_left_corner":c+='<polygon class="rs_addon_point1" points="0,0 500,0 500,500" style="fill:'+h+"; stroke:"+h+
'; stroke-width: 1" /><polygon class="rs_addon_point2" points="0,0 0,500 500,500" style="fill:'+h+"; stroke:"+h+'; stroke-width: 1" />';isCorner=!0;v=G;b.x=500;u.x=-500;break;case "split_right_corner":c+='<polygon class="rs_addon_point1" points="0,0 500,0 0,500" style="fill:'+h+"; stroke:"+h+'; stroke-width: 1" /><polygon class="rs_addon_point2" points="500,0 500,500 0,500" style="fill:'+h+"; stroke:"+h+'; stroke-width: 1" />';isCorner=!0;v=G;b.x=-500;u.x=500;break;case "shrink_circle":var e=2*Math.max(a.width(),
a.height());c+="width: "+e+"px; height: "+e+"px; top: 50%; left: 50%; transform: translate(-50%, -50%); border-radius: 50%";b.width="0";b.height="0";break;case "expand_circle":H=!0;v=R;a.css("clip-path","circle(0% at 50% 50%)");break;case "left_to_right":c+="width: 100%; height: 100%; top: 0; left: 0";b.left="100%";break;case "right_to_left":c+="width: 100%; height: 100%; top: 0; left: 0";b.width="0%";break;case "top_to_bottom":c+="width: 100%; height: 100%; top: 0; left: 0";b.top="100%";break;case "bottom_to_top":c+=
"width: 100%; height: 100%; top: 0; left: 0";b.height="0%";break;case "tlbr_skew":e=Math.atan2(a.width(),a.height());c+="width: 200%; height: 200%; top: 0%; left: -100%; transform: skew(-"+e+"rad)";b.left="100%";break;case "trbl_skew":e=Math.atan2(a.width(),a.height());c+="width: 200%; height: 200%; top: 0%; right: -100%; transform: skew("+e+"rad)";b.right="100%";break;case "bltr_skew":e=Math.atan2(a.width(),a.height());c+="width: 200%; height: 200%; bottom: -100%; left: 0%; transform: skew("+e+"rad)";
b.bottom="100%";break;case "brtl_skew":e=Math.atan2(a.width(),a.height()),c+="width: 200%; height: 200%; bottom: -100%; right: 0; transform: skew(-"+e+"rad)",b.bottom="100%"}d.overlay_enabled&&(w=p('<div class="rsaddon-revealer-overlay" style="background: '+d.overlay_color+'" />').appendTo(n));c+=I?"</svg>":'" />';c=p(c).appendTo(n);if(!H||a.css("clip-path")){z&&(t=p(t+'" />').appendTo(n));n.appendTo(a);if(!A)a.one("revolution.slide.onafterswap",B);f&&f.length&&(g.loader=f);S?C=setTimeout(function(){J=
!0;K&&a.revstart()},k):A&&(a.removeClass("rs_addon_revealer_special"),B())}}}function B(){if(!q)if("off"===g.stopLoop&&a.revpause(),f&&f.length||(f=a.find(".tp-loader")),f.length){g.loader=f;var e={opacity:0,ease:punchgs.Power3.easeOut,onComplete:v};D&&k&&(e.delay=k);punchgs.TweenLite.to(f,.3,e)}else D&&k?C=setTimeout(v,k):v()}function R(){if(!q){w&&E();b.point=100;var e={point:0};x=new punchgs.TweenLite(e,l,b);x.eventCallback("onUpdate",function(){a.css("clip-path","circle("+e.point+"% at 50% 50%)")})}}
function G(){q||(w&&E(),punchgs.TweenLite.to(n.find(".rs_addon_point1"),l,b),punchgs.TweenLite.to(n.find(".rs_addon_point2"),l,u))}function E(){var a=d.overlay_duration,c=d.overlay_easing.split("."),b=d.overlay_delay;isNaN(b)&&(b=0);b=.001*parseInt(b,10);isNaN(a)&&(a="300");a=.001*parseInt(a,10);punchgs.TweenLite.to(w,a,{opacity:0,ease:punchgs[c[0]][c[1]],delay:b,onComplete:L})}function M(){a.removeClass("rs_addon_reveal rs_addon_revealer_special");a.find(".tp-loader").css("opacity",1);n&&n.remove();
"off"===g.stopLoop&&a.revresume();a=g=null}function L(){w&&!N||M();N=!0}function O(){window.removeEventListener("resize",O);clearTimeout(C);q=!0;a.off("revolution.slide.onloaded",y).off("revolution.slide.onafterswap",B);punchgs.TweenLite.killTweensOf(p(".rs_addon_revealer").find("*"));x&&(x.eventCallback("onUpdate",null),x.kill(),x=null);M()}function P(){f=a.find(".tp-loader");f.length?Q(f):window.requestAnimationFrame(P)}function Q(b){b&&b.length?b[0].className="tp-loader":b=p('<div class="tp-loader" />').appendTo(a);
b.html(F.replace(/{{color}}/g,d.spinnerColor));g.loader=b}if(p){var g=a[0].opt,K,J;a.on("scriptsloaded",function(){K=!0;J&&a.revstart()});if(window.hasOwnProperty("RsAddonRevealerCustom")){var d=window.RsAddonRevealerCustom;var m=document.URL.split("?");2===m.length&&window.RsAddonRevealerCustom.hasOwnProperty(m[1])&&"itm_1"!==m[1]?(d=window.RsAddonRevealerCustom[m[1]],d.hasOwnProperty("spinner")&&(F=d.spinnerHTML)):d=g.revealer}else d=g.revealer;var r=d.direction,k=d.delay,f,N,C;"default"!==d.spinner&&
("off"!==g.spinner?window.requestAnimationFrame(P):(g.spinner="on",Q()));if("none"===r)a.one("revolution.slide.onloaded",function(){f&&f.length&&(g.loader=f)});else{a.addClass("rs_addon_reveal").find("li").first().attr("fstransition","notransition").data("fstransition","notransition");var n=p('<div class="rs_addon_revealer" />'),z=-1!==r.search("open"),I=-1!==r.search("corner");m=d.easing.split(".");var A="fullwidth"===g.sliderLayout&&-1!==r.search("skew"),b={ease:punchgs[m[0]][m[1]],onComplete:L},
u={ease:punchgs[m[0]][m[1]]},D=/skew|shrink/.test(r),l=d.duration,h=d.color,v=function(){q||(w&&E(),punchgs.TweenLite.to(c,l,b),z&&punchgs.TweenLite.to(t,l,u))},c="",t="",w,H,q,x;isNaN(l)&&(l="300");l=.001*parseInt(l,10);isNaN(k)&&(k=0);k=.001*parseInt(k,10);I?c='<svg version="1.1" viewBox="0 0 500 500" preserveAspectRatio="none">':(c='<div style="background: '+h+"; ",z&&(t='<div style="background: '+h+"; "));if(D)if(window.addEventListener("resize",O),A)a.addClass("rs_addon_revealer_special").one("revolution.slide.onafterswap",
y);else a.one("revolution.slide.onloaded",y);else{if(k){var S=!0;g.waitForInit=!0;a.height("100%")}y()}}}}})();