package com.example.gtics_ta.DTO;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Time;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AsignacionCoordinadorDTO {
    
    // Información de la semana
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate fechaInicioSemana;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate fechaFinSemana;
    
    // Información del coordinador
    private Integer coordinadorId;
    private String coordinadorNombre;
    
    // Información del espacio
    private Integer espacioId;
    private String espacioNombre;
    private String espacioUbicacion;
    
    // Horarios de trabajo
    @DateTimeFormat(pattern = "HH:mm")
    private Time horaEntrada;
    
    @DateTimeFormat(pattern = "HH:mm")
    private Time horaSalida;
    
    // Días de la semana (para selección múltiple)
    private List<String> diasSemana;
    
    // Observaciones
    private String observaciones;
    
    // Constructor por defecto
    public AsignacionCoordinadorDTO() {}
    
    // Constructor con parámetros básicos
    public AsignacionCoordinadorDTO(Integer coordinadorId, String coordinadorNombre, 
                                   Integer espacioId, String espacioNombre) {
        this.coordinadorId = coordinadorId;
        this.coordinadorNombre = coordinadorNombre;
        this.espacioId = espacioId;
        this.espacioNombre = espacioNombre;
    }
}
