<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7dca676d-8604-422e-910e-8e4005545602" name="Changes" comment="S3 credentials eliminada">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/gtics_ta/Config/WebSecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/gtics_ta/Config/WebSecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/templates/admin/agregarservicio_debug.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/templates/admin/agregarservicio_debug.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/templates/login/login.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/templates/login/login.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="preeliminar" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;arzuvi71&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Elpapidecristhian/New_Fibra.git&quot;,
    &quot;accountId&quot;: &quot;61d43d33-1009-4cc4-bbc8-cd2249bd7834&quot;
  }
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2wDPGRYYCS3ys85qzMOA8IpvLfL" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;JavaScript Debug.login.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript Debug.perfil.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript Debug.principal.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript Debug.servicios.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript Debug.vecino_espacios_deportivos.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript Debug.vecino_perfil.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript Debug.vecino_reservas.html.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.GticsTaApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;proyecto&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/CICLO 2 FACI-PUCP/IWEB/labs/New_Fibra/GTICS_TA/src/main/resources/static&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\static" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\templates\static" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\templates\images\auth-bg" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\templates\main\main" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\static" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\templates\static" />
      <recent name="D:\CICLO 2 FACI-PUCP\IWEB\labs\New_Fibra\GTICS_TA\src\main\resources\templates\main" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.GticsTaApplication">
    <configuration name="agregarservicio_debug.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/GTICS_TA/templates/admin/agregarservicio_debug.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="principal.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/GTICS_TA/templates/main/Coordinador/principal.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="servicios.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/GTICS_TA/templates/admin/servicios.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="vecino_espacios_deportivos.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/GTICS_TA/templates/main/vecino_espacios_deportivos.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="vecino_reservas.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/GTICS_TA/templates/main/vecino_reservas.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="GticsTaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="GTICS_TA" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.gtics_ta.GticsTaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JavaScript Debug.agregarservicio_debug.html" />
        <item itemvalue="JavaScript Debug.vecino_reservas.html" />
        <item itemvalue="JavaScript Debug.vecino_espacios_deportivos.html" />
        <item itemvalue="JavaScript Debug.servicios.html" />
        <item itemvalue="JavaScript Debug.principal.html" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7dca676d-8604-422e-910e-8e4005545602" name="Changes" comment="" />
      <created>1745573075427</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745573075427</updated>
      <workItem from="1745573076496" duration="4714000" />
      <workItem from="1745602825351" duration="13091000" />
      <workItem from="1746731270142" duration="11687000" />
      <workItem from="1746812105509" duration="17636000" />
      <workItem from="1746837045304" duration="1765000" />
      <workItem from="1747245078982" duration="32296000" />
      <workItem from="1747372374952" duration="10658000" />
      <workItem from="1747417489053" duration="10484000" />
      <workItem from="1747441327724" duration="3130000" />
      <workItem from="1747495938517" duration="1760000" />
      <workItem from="1747535041426" duration="22349000" />
      <workItem from="1747584406204" duration="28926000" />
      <workItem from="1748620157941" duration="12717000" />
      <workItem from="1748929314565" duration="12568000" />
      <workItem from="1749073732053" duration="4988000" />
      <workItem from="1749099916752" duration="13935000" />
      <workItem from="1749174813240" duration="11761000" />
      <workItem from="1749228708480" duration="10752000" />
      <workItem from="1749245809077" duration="3933000" />
      <workItem from="1749256049071" duration="918000" />
      <workItem from="1749410707088" duration="19740000" />
      <workItem from="1749535206356" duration="11320000" />
      <workItem from="1749587048598" duration="14951000" />
      <workItem from="1749762453379" duration="459000" />
      <workItem from="1749765248378" duration="2829000" />
      <workItem from="1749799575067" duration="3357000" />
    </task>
    <task id="LOCAL-00001" summary="Detalles reserva historial y listar">
      <option name="closed" value="true" />
      <created>1745613283692</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1745613283692</updated>
    </task>
    <task id="LOCAL-00002" summary="Arreglo de un html">
      <option name="closed" value="true" />
      <created>1745613451506</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1745613451506</updated>
    </task>
    <task id="LOCAL-00003" summary="Actualizacion">
      <option name="closed" value="true" />
      <created>1745619895015</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1745619895015</updated>
    </task>
    <task id="LOCAL-00004" summary="Paginas html coordinadro y login linkeado">
      <option name="closed" value="true" />
      <created>1746781173232</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1746781173232</updated>
    </task>
    <task id="LOCAL-00005" summary="añadidura de imagen background para login">
      <option name="closed" value="true" />
      <created>1746781668176</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1746781668176</updated>
    </task>
    <task id="LOCAL-00006" summary="Entity para coordinador">
      <option name="closed" value="true" />
      <created>1746782436342</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1746782436342</updated>
    </task>
    <task id="LOCAL-00007" summary="Modificaciones CRUD vecino">
      <option name="closed" value="true" />
      <created>1746815278969</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1746815278969</updated>
    </task>
    <task id="LOCAL-00008" summary="CRUDS para perfil coordinador y arreglo de carpetas CSS">
      <option name="closed" value="true" />
      <created>1746826149354</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746826149354</updated>
    </task>
    <task id="LOCAL-00009" summary="Linkeado de CSS para pagina principal coordinador">
      <option name="closed" value="true" />
      <created>1746827909139</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746827909140</updated>
    </task>
    <task id="LOCAL-00010" summary="CRUDS coordinador">
      <option name="closed" value="true" />
      <created>1746830756919</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746830756920</updated>
    </task>
    <task id="LOCAL-00011" summary="Arreglos con el Jp">
      <option name="closed" value="true" />
      <created>1747246412093</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1747246412093</updated>
    </task>
    <task id="LOCAL-00012" summary="Arreglos con el Jp">
      <option name="closed" value="true" />
      <created>1747246483983</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1747246483983</updated>
    </task>
    <task id="LOCAL-00013" summary="Arreglos 2.0">
      <option name="closed" value="true" />
      <created>1747253723442</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1747253723442</updated>
    </task>
    <task id="LOCAL-00014" summary="Perfil coordinador">
      <option name="closed" value="true" />
      <created>1747262980715</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1747262980715</updated>
    </task>
    <task id="LOCAL-00015" summary="Creación de Web Security y sesión para coordinador">
      <option name="closed" value="true" />
      <created>1747295385568</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1747295385568</updated>
    </task>
    <task id="LOCAL-00016" summary="Modificaciones en html de login">
      <option name="closed" value="true" />
      <created>1747298479461</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747298479461</updated>
    </task>
    <task id="LOCAL-00017" summary="Crud perfil terminado y avance de security and session">
      <option name="closed" value="true" />
      <created>1747385754286</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1747385754286</updated>
    </task>
    <task id="LOCAL-00018" summary="Security implementado y con redireccion">
      <option name="closed" value="true" />
      <created>1747427141286</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1747427141287</updated>
    </task>
    <task id="LOCAL-00019" summary="ultima modificacion de la rama">
      <option name="closed" value="true" />
      <created>1747535989241</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1747535989241</updated>
    </task>
    <task id="LOCAL-00020" summary="Avance login y perfil">
      <option name="closed" value="true" />
      <created>1747549115828</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1747549115828</updated>
    </task>
    <task id="LOCAL-00021" summary="loader y htmls login linkeados y con thymeleaf">
      <option name="closed" value="true" />
      <created>1747555050147</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1747555050147</updated>
    </task>
    <task id="LOCAL-00022" summary="Commit antes de probar geolocalizacion">
      <option name="closed" value="true" />
      <created>1747556379646</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747556379647</updated>
    </task>
    <task id="LOCAL-00023" summary="Combobox de Tipo espacio">
      <option name="closed" value="true" />
      <created>1747597900381</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747597900381</updated>
    </task>
    <task id="LOCAL-00024" summary="geolocalizacion">
      <option name="closed" value="true" />
      <created>1747606007861</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747606007862</updated>
    </task>
    <task id="LOCAL-00025" summary="Ultima modificación">
      <option name="closed" value="true" />
      <created>1748620352119</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1748620352119</updated>
    </task>
    <task id="LOCAL-00026" summary="-">
      <option name="closed" value="true" />
      <created>1748929665778</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1748929665778</updated>
    </task>
    <task id="LOCAL-00027" summary="Mapa funcionando con open">
      <option name="closed" value="true" />
      <created>1748943292379</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1748943292379</updated>
    </task>
    <task id="LOCAL-00028" summary="Mapa funciona con google maps y acepta 4 imagenes max">
      <option name="closed" value="true" />
      <created>1748944667821</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1748944667821</updated>
    </task>
    <task id="LOCAL-00029" summary="nuevos servicios desarrollado pero falta url de ubicacion">
      <option name="closed" value="true" />
      <created>1749116495463</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1749116495463</updated>
    </task>
    <task id="LOCAL-00030" summary="nuevos servicios desarrollado pero falta url de ubicacion">
      <option name="closed" value="true" />
      <created>1749116667188</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1749116667193</updated>
    </task>
    <task id="LOCAL-00031" summary="Form observaciones funcional y creación de historial de observaciones">
      <option name="closed" value="true" />
      <created>1749177853847</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1749177853847</updated>
    </task>
    <task id="LOCAL-00032" summary="Form observaciones funcional y creación de historial de observaciones">
      <option name="closed" value="true" />
      <created>1749187984720</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1749187984720</updated>
    </task>
    <task id="LOCAL-00033" summary="WebConfig">
      <option name="closed" value="true" />
      <created>1749190237118</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1749190237118</updated>
    </task>
    <task id="LOCAL-00034" summary="Merge completado y web security funcional">
      <option name="closed" value="true" />
      <created>1749240601190</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1749240601191</updated>
    </task>
    <task id="LOCAL-00035" summary="Entidades añadidas">
      <option name="closed" value="true" />
      <created>1749240663484</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1749240663484</updated>
    </task>
    <task id="LOCAL-00036" summary="ultimos cambios">
      <option name="closed" value="true" />
      <created>1749248367310</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1749248367310</updated>
    </task>
    <task id="LOCAL-00037" summary="-">
      <option name="closed" value="true" />
      <created>1749248634889</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1749248634890</updated>
    </task>
    <task id="LOCAL-00038" summary="Entitys completos">
      <option name="closed" value="true" />
      <created>1749428271802</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1749428271802</updated>
    </task>
    <task id="LOCAL-00039" summary="Pagina agregar servicio reparada">
      <option name="closed" value="true" />
      <created>1749430277994</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1749430277994</updated>
    </task>
    <task id="LOCAL-00040" summary="Pagina reservas completada">
      <option name="closed" value="true" />
      <created>1749549189926</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1749549189929</updated>
    </task>
    <task id="LOCAL-00041" summary="Pagina reservas completada 2">
      <option name="closed" value="true" />
      <created>1749550013651</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1749550013655</updated>
    </task>
    <task id="LOCAL-00042" summary="Seccion de pagos implementada y funcional">
      <option name="closed" value="true" />
      <created>1749603808130</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1749603808133</updated>
    </task>
    <task id="LOCAL-00043" summary="Seccion de pagos implementada y funcional 2.0">
      <option name="closed" value="true" />
      <created>1749606055097</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1749606055097</updated>
    </task>
    <task id="LOCAL-00044" summary="S3 credentials deshabilitada por seguridad">
      <option name="closed" value="true" />
      <created>1749606494473</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1749606494473</updated>
    </task>
    <task id="LOCAL-00045" summary="S3 credentials deshabilitada por seguridad">
      <option name="closed" value="true" />
      <created>1749766894705</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1749766894705</updated>
    </task>
    <option name="localTasksCounter" value="46" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Crud perfil terminado y avance de security and session" />
    <MESSAGE value="Security implementado y con redireccion" />
    <MESSAGE value="ultima modificacion de la rama" />
    <MESSAGE value="Avance login y perfil" />
    <MESSAGE value="loader y htmls login linkeados y con thymeleaf" />
    <MESSAGE value="Commit antes de probar geolocalizacion" />
    <MESSAGE value="Combobox de Tipo espacio" />
    <MESSAGE value="geolocalizacion" />
    <MESSAGE value="Ultima modificación" />
    <MESSAGE value="Mapa funcionando con open" />
    <MESSAGE value="Mapa funciona con google maps y acepta 4 imagenes max" />
    <MESSAGE value="nuevos servicios desarrollado pero falta url de ubicacion" />
    <MESSAGE value="Form observaciones funcional y creación de historial de observaciones" />
    <MESSAGE value="WebConfig" />
    <MESSAGE value="Merge completado y web security funcional" />
    <MESSAGE value="Entidades añadidas" />
    <MESSAGE value="ultimos cambios" />
    <MESSAGE value="-" />
    <MESSAGE value="Entitys completos" />
    <MESSAGE value="Pagina agregar servicio reparada" />
    <MESSAGE value="Pagina reservas completada" />
    <MESSAGE value="Pagina reservas completada 2" />
    <MESSAGE value="Seccion de pagos implementada y funcional" />
    <MESSAGE value="Seccion de pagos implementada y funcional 2.0" />
    <MESSAGE value="S3 credentials deshabilitada por seguridad" />
    <option name="LAST_COMMIT_MESSAGE" value="S3 credentials deshabilitada por seguridad" />
  </component>
</project>