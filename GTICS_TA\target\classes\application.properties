spring.application.name=GTICS_TA
spring.datasource.url=*********************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.open-in-view=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/

# Configuración de Google Maps
# Para desarrollo: usar la API key de prueba
# Para producción: la empresa debe reemplazar con su API key
google.maps.api.key=${GOOGLE_MAPS_API_KEY:AIzaSyB8gglF9Vdl-dlam3umZipOmq92nPlCEng}
google.maps.enabled=${GOOGLE_MAPS_ENABLED:true}
spring.thymeleaf.check-template-location=true
spring.thymeleaf.enabled=true
spring.thymeleaf.template-resolver-order=1
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=cgxu gnnj hhus tuhy
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Configuración de AWS S3
# Credenciales de AWS Academy
aws.s3.bucket.name=gtics-images-bucket-2025
aws.s3.region=us-east-1
aws.access.key.id=********************
aws.secret.access.key=2EFM7VbQqfr2hnu/+UvjuvenZ4woR86Fp1KclNwr
aws.session.token=IQoJb3JpZ2luX2VjEOz//////////wEaCXVzLXdlc3QtMiJGMEQCIGjBo4U2qNqWHo0ZpME7SxapuPd7EPuQyzftAtVoNdQDAiB8C4WXWfOMfFBscLR9i1qMl9W3fynSSA5vAkSFnMLfqiqxAgjF//////////8BEAAaDDczMDMzNTUxMzE4NyIMNLvhBb5+DMjBPNdrKoUCmACUe221raGZHRlfZNUrFpZsgmu5mp4wh4fROsBhE2Qrme9HP/36nCPtAKBbecBt0qCcz3TAwlL/0pu+qy+ZY8yTmGlUH7rO4AL7XgMgUypx4uh00OyE8INWhzB7crZY/7TqYAHyNvJEa8A6pILEXajD9/7ryLsDHFiN1IGDSihQxOpVHNa3D+HB8UqzK5NvWQYLgoPTqTDVfhe9a5avaAxCs8e6kmrYBFLCcbaplA5BBsFZ0b2cyL/hfpkHGOODwO3lJa11NqPwISVJBGXZAe8hJSosZ5Qvq8xhMCXWCaBLrGRp+6vmS+BhQ0iUy0ZM0O+4MUFKLr5it1JBTjg3Bt+mLKecMJGgosIGOp4Bm0tlSMdMEVnVBd5NH0bmHY4IuzblyEAN3UhPKJ6O2igIZIwrSaQMfw4emt5Zy+7C5V82VnKigIoGGR6l3IqqHorFIq8B2LVsD11TO0L3uDjKQhymFDamC2TqUXfBMbMvpiiNU2dPAYizgxwckuCBJPP5CNELfCAbxr925Zu58q8dni8piVE1g/zjdMLIXREa0e11J0+d3dLXPY5fQyM=
aws.s3.url.expiration.minutes=60

# Configuración de logging para debug
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web=DEBUG
logging.level.org.springframework.security.authentication=DEBUG
logging.level.com.example.gtics_ta=DEBUG
logging.level.org.springframework.jdbc=DEBUG
