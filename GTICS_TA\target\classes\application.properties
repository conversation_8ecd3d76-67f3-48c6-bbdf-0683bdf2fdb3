spring.application.name=GTICS_TA
spring.datasource.url=*********************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.open-in-view=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/

# Configuración de Google Maps
# Para desarrollo: usar la API key de prueba
# Para producción: la empresa debe reemplazar con su API key
google.maps.api.key=${GOOGLE_MAPS_API_KEY:AIzaSyB8gglF9Vdl-dlam3umZipOmq92nPlCEng}
google.maps.enabled=${GOOGLE_MAPS_ENABLED:true}
spring.thymeleaf.check-template-location=true
spring.thymeleaf.enabled=true
spring.thymeleaf.template-resolver-order=1
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=cgxu gnnj hhus tuhy
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Configuración de AWS S3
# Credenciales de AWS Academy
aws.s3.bucket.name=gtics-images-bucket-2025
aws.s3.region=us-east-1
# Eliminaci�n para push
aws.access.key.id=********************
aws.secret.access.key=mm9TlWXFMtdgRZDQWyPXu3LPctIx9ogGp+oYdvYy
aws.session.token=IQoJb3JpZ2luX2VjECgaCXVzLXdlc3QtMiJHMEUCIFpDqDO7nfQ28LXRJWUjWNIs3Gt1Ijq0dmfTvZZQdW07AiEAoqv4M3AhHQHnvqn5yJz4avbOtlzbFU9gvLIKX/b+TmwqqAIIERAAGgw3MzAzMzU1MTMxODciDO9CO02H7CcbHpFzHCqFAif56N1Nw6toN60/I8ZJmBNx3qBtFpgjWxt25uhQjr+qX/ixFfb8U3HZhSoNVy7pT1Pin/ncPJpKZnl4QwwUq3X5FrPIQopgFNcVWKrdl6nD7Pqs5SXRAlLR1/u3U3AFZ9St2XHuWm9XCn04tO6SS1Vpvj2NcAsTbotq2UaDJOe4V5u1OKKwGoxlhHPzYkk7ho7poW4hJTpkHQPEE6r7PhNHg3UA26G3yLR6s8Ls7dPtgDlhdTcHFE6662xZlJzNny860lr3KwSPabg1dGfPIL3ADng39XPxAOcrRwFZK4nKyiZoo//IIyCLfPfDueMSujSm6mMzHMmq9i/zAfNKofyFF+z8szCora/CBjqdATZYG81PmjLR+ZJ3q7g5+RE6KvszacmBBK/I+/Tgm/5rK1zcGqGTgsHT0Vn5oU9zNUQrdZ8d1/ZNclOxciETT6gSgjoZio9Ihns+smRJlbhI22KAJoG1ZZRTu0n5MGbpH5xSzycN+BqW1NXOdfMqXjRaPG7rdxafv+JVNJ33TwLcj7NCP1WcExWJow5aUUKRhBb/YOA24sqLeJHXk/c=
aws.s3.url.expiration.minutes=60

# Configuración de logging para debug
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web=DEBUG
logging.level.org.springframework.security.authentication=DEBUG
logging.level.com.example.gtics_ta=DEBUG
logging.level.org.springframework.jdbc=DEBUG
