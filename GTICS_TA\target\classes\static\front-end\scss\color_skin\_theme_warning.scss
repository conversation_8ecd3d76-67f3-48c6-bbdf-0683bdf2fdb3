/**************************************
Theme Warning Color
**************************************/
.bg-gradient-warning  
{
	background: $theme-warning-grd;
}
.bg-light-body  {
    background: transparent;
}
.theme-warning{ 
    .bg-gradient-warning{@extend .bg-gradient-warning}
    .art-bg{@extend .bg-gradient-warning}
    &.fixed {        
        .main-header {
            background: transparent;
        }
    }
    .main-header{
        background: transparent;
    }
}

.theme-warning.onlyheader .art-bg{
	background-image: none;
}

.bg-gradient-warning-dark
{
	background-image: $theme-warning-grd-dark;
}
.bg-dark-body  {
    background: $body-dark;
}
.dark-skin{
&.theme-warning{ 
    .bg-gradient-warning{@extend .bg-gradient-warning-dark}
    .art-bg{@extend .bg-gradient-warning-dark}
    &.fixed {        
        .main-header {
            background: transparent;
        }
    }
    .main-header{
        background: transparent;
    }
}
}

// Small devices
@include screen-sm-max {
    .theme-warning{ 
        &.fixed {        
            .main-header {
                background-image: $light3;
                &.navbar{
                    background: none;
                }
            }
        }        
    }
        
    .dark-skin{
    &.theme-warning{ 
        &.fixed {        
            .main-header {
                background-image: $body-dark;
            }
        }
    }
    }
}


.theme-warning{
    a{          
        @include hover-state{
            color: $theme-warning-primary;
        }         
    }
    
    .main-sidebar{        
        .svg-icon {
            filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg);
            @include hover-state{
                filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg);
            }
        }
        a  {
            @include hover-state{
                .svg-icon{
                    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg);
                }
            }
        }
    }
    .svg-icon {
        filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg);
        @include hover-state{
            filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg);
        }
    }
    a  {
        @include hover-state{
            .svg-icon{
                filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg);
            }
        }
    }
}
.theme-warning{
    &.light-skin {
        .sidebar-menu{
            >li{
                &.active.treeview {
                    >a{
                        background: transparent;
                        color: $light5 !important;
                        > i{
                           color: $white; 
                        }
                        > svg{
                           color: $white; 
                            fill: rgba(1, 104, 250, 0.2);
                        }
                        &:after{
                            border-color: transparent #fafafa transparent transparent !important;
                        }
                    }
                }
                &.treeview{
                    .treeview-menu{
                        li{
                            a{
                                color: $light5;
                            }
                        }
                    }
                }
            } 
        }
         &.sidebar-mini{
            &.sidebar-collapse{
                .sidebar-menu{
                    >li.active{
                        >a{
                            >span{
                                background: $theme-warning-primary !important;
                            }
                        }
                    }
                }
            }
        }
    }
    
    &.dark-skin {
        .sidebar-menu{
            >li{
                &.active{
                    >a{
                        &:after{
                            border-color: transparent lighten($black, 20%) transparent transparent !important;
                        }
                    }
                    &.treeview {
                        >a{
                            background: transparent;
                            color: $light5 !important;
                            > i{
                               color: $white; 
                            }
                            &:after{
                                border-color: transparent #fafafa transparent transparent !important;
                            }
                        }
                        .treeview-menu{
                            li{
                                a{
                                    color: $light5;
                                }
                            }
                        }
                    }
                }
            } 
        }
         &.sidebar-mini{
            &.sidebar-collapse{
                .sidebar-menu{
                    >li.active{
                        >a{
                            >span{
                                background: $theme-warning-primary !important;
                            }
                        }
                    }
                }
            }
        }
    }    
    &.light-skin {
        .sidebar-menu{
            >li{
                @include hover-active-state{                    
                    background-color: rgba($theme-warning-primary, 0.0);
                    color: rgba($white, 1);
                    border-left: 5px solid rgba($theme-warning-primary, 0);
                    a{
                        color: rgba($white, 1);
                    }
                }
                &.active{
                    background-color: rgba($theme-warning-primary, 0.0);
                    color: rgba($white, 1);
                    border-left: 5px solid rgba($theme-warning-primary, 1);
                    a{
                        color: rgba($white, 1);
                        background-color: transparent;
                        > i{
                           color: $white ;
                           background-color: rgba($theme-warning-primary, 0) ;
                        }                        
                        > svg{
                           color: $white; 
                            fill: rgba(1, 104, 250, 0.2);
                        }
                        img.svg-icon
                        {                            
                            filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg);
                        }
                    }
                    .treeview-menu{
                        li.active{
                            background-color: rgba($theme-warning-primary, 0.0);
                            color: rgba($white, 1);
                            a{
                                color: rgba($white, 1);                                
                                > i{
                                   color: rgba($white, 1) ;
                                   background-color: rgba($theme-warning-primary, 0) ;
                                } 
                            }
                        }
                        li{
                            a{                                
                                > i{
                                   color: $light5 ;
                                   background-color: rgba($theme-warning-primary, 0) ;
                                } 
                            }
                        }
                        li.treeview{
                            &.active{
                                background-color: rgba($theme-warning-primary, 0.0);
                                color: rgba($white, 1);
                                a{
                                    color: rgba($white, 1);                                
                                    > i{
                                       color: rgba($white, 1) ;
                                       background-color: rgba($theme-warning-primary, 0) ;
                                    } 
                                }
                            }
                            .treeview-menu{
                                li{                                    
                                    &.active{
                                        background-color: rgba($theme-warning-primary, 0.0);
                                        color: rgba($white, 1);
                                        a{
                                            color: rgba($white, 1);                                
                                            > i{
                                               color: rgba($white, 1) ;
                                               background-color: rgba($theme-warning-primary, 0) ;
                                            } 
                                        }
                                    }
                                    a{    
                                        color: $light5 ;
                                        > i{
                                           color: $light5 ;
                                           background-color: rgba($theme-warning-primary, 0) ;
                                        } 
                                    }
                                }
                            }
                        }
                    }
                }
            } 
        }
    }
    &.rtl.light-skin {
        .sidebar-menu{
            >li{
                @include hover-active-state{    
                    border-left: 0px solid rgba($theme-warning-primary, 0);
                    border-right: 5px solid rgba($theme-warning-primary, 0);
                }
                &.active{
                    border-left: 0px solid rgba($theme-warning-primary, 1);
                    border-right: 5px solid rgba($theme-warning-primary, 1);
                }
            } 
        }
    }
    &.dark-skin {
        .sidebar-menu{
            >li{
                &.active{
                    background-color: rgba($theme-warning-primary, 0.0);
                    color: rgba($white, 1);
                    border-left: 5px solid rgba($theme-warning-primary, 1);
                    a{
                        color: rgba($white, 1);
                        background-color: transparent;
                        > i{
                           color: rgba($white, 1) ;
                        }
                        > svg{
                           color: $white; 
                            fill: rgba(1, 104, 250, 0.2);
                        }                        
                        img.svg-icon
                        {                            
                            filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg);
                        }
                    }
                    .treeview-menu{
                        li.active{
                            background-color: rgba($theme-warning-primary, 0.0);
                            color: rgba($white, 1);
                            a{
                                color: rgba($white, 1) !important;
                            }
                        }
                    }
                }
            } 
        }
    }
    &.rtl.dark-skin {
        .sidebar-menu{
            >li{
                &.active{
                    border-left: 0px solid rgba($theme-warning-primary, 1);
                    border-right: 5px solid rgba($theme-warning-primary, 1);
                }
            } 
        }
    }
}

@include screen-md { 
    .sidebar-mini{
        &.sidebar-collapse{
            .sidebar-menu{
                >li.active.menu-open{
                    background-color: rgba($theme-warning-primary, 0.2);
                    color: rgba($theme-warning-primary, 1);
                }
            }
        }
    }
}
/*---Main Nav---*/
.theme-warning{
    .sm-blue{        
        li.current, li.highlighted{
            > a{
                background: $theme-warning-primary;
                color: $white !important;
                @include hover-state{
                    background: $theme-warning-primary;
                    color: $white !important;
                }
            }
        }
        a{
            &.current, &.highlighted{
                background: $theme-warning-primary;
                color: $white !important;
            }
            @include hover-state{
                background: $theme-warning-primary;
                color: $white !important;
            }
        }
        ul{
            a{
                @include hover-state{
                    background: $light2;
                    color: $theme-warning-primary !important;
                }
                &.highlighted{
                    background: $light2;
                    color: $theme-warning-primary !important;
                }
            }
        }
    }
}
.dark-skin{
    &.theme-warning{
        .sm-blue{
            a{
                &.current, &.highlighted{
                    background: $theme-warning-primary;
                    color: $white !important;
                }
                @include hover-state{
                    background: $theme-warning-primary;
                    color: $white !important;
                }
            }
            ul{
                a{
                    @include hover-state{
                        background: darken($dark2,25%);
                        color: $theme-warning-primary !important;
                    }
                    &.highlighted{ 
                        background: darken($dark2,25%);
                        color: $theme-warning-primary !important;
                    }
                }
            }
        }
    }
}
    /*---Primary Button---*/
.theme-warning {
    .btn-link {
        color: $theme-warning-primary;
    }
    .btn-primary {
        background-color: $theme-warning-primary;
        border-color: $theme-warning-primary;
        color: $white;
        @include hover-full-state{
            background-color: darken($theme-warning-primary, 10%) !important;
            border-color: darken($theme-warning-primary, 10%) !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-primary, 20%);
            border-color: $theme-warning-primary;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-primary, 20%);
            border-color: $theme-warning-primary;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-primary{
        &.dropdown-toggle{
            background-color: darken($theme-warning-primary, 10%) !important;
            border-color: darken($theme-warning-primary, 10%) !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-primary{
        color: $theme-warning-primary;
        background-color: transparent;
        border-color: $theme-warning-primary !important;        
        @include hover-active-state{
            background-color: darken($theme-warning-primary, 10%) !important;
            border-color: darken($theme-warning-primary, 10%) !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-primary{
        &.dropdown-toggle{
            background-color: darken($theme-warning-primary, 10%) !important;
            border-color: darken($theme-warning-primary, 10%) !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-primary{
        color: $theme-warning-primary !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: darken($theme-warning-primary, 10%) !important;
            border-color: darken($theme-warning-primary, 10%) !important;
            color: $white !important;
        }
    }
    }
}

/*---info Button---*/
.theme-warning {
    .btn-info {
        background-color: $theme-warning-info;
        border-color: $theme-warning-info;
        color: $white;
        @include hover-full-state{
            background-color: darken($theme-warning-info, 10%) !important;
            border-color: darken($theme-warning-info, 10%) !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-info, 20%);
            border-color: $theme-warning-info;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-info, 20%);
            border-color: $theme-warning-info;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-info{
        &.dropdown-toggle{
            background-color: darken($theme-warning-info, 10%) !important;
            border-color: darken($theme-warning-info, 10%) !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-info{
        color: $theme-warning-info;
        background-color: transparent;
        border-color: $theme-warning-info !important;        
        @include hover-active-state{
            background-color: darken($theme-warning-info, 10%) !important;
            border-color: darken($theme-warning-info, 10%) !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-info{
        &.dropdown-toggle{
            background-color: darken($theme-warning-info, 10%) !important;
            border-color: darken($theme-warning-info, 10%) !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-info{
        color: $theme-warning-info !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: darken($theme-warning-info, 10%) !important;
            border-color: darken($theme-warning-info, 10%) !important;
            color: $white !important;
        }
    }
    }
}

/*---Success Button---*/
.theme-warning {
    .btn-success {
        background-color: $theme-warning-success;
        border-color: $theme-warning-success;
        color: $white;
        @include hover-full-state{
            background-color: darken($theme-warning-success, 10%) !important;
            border-color: darken($theme-warning-success, 10%) !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-success, 20%);
            border-color: $theme-warning-success;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-success, 20%);
            border-color: $theme-warning-success;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-success{
        &.dropdown-toggle{
            background-color: darken($theme-warning-success, 10%) !important;
            border-color: darken($theme-warning-success, 10%) !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-success{
        color: $theme-warning-success;
        background-color: transparent;
        border-color: $theme-warning-success !important;        
        @include hover-active-state{
            background-color: darken($theme-warning-success, 10%) !important;
            border-color: darken($theme-warning-success, 10%) !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-success{
        &.dropdown-toggle{
            background-color: darken($theme-warning-success, 10%) !important;
            border-color: darken($theme-warning-success, 10%) !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-success{
        color: $theme-warning-success !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: darken($theme-warning-success, 10%) !important;
            border-color: darken($theme-warning-success, 10%) !important;
            color: $white !important;
        }
    }
    }
}


/*---Danger Button---*/
.theme-warning {
    .btn-danger {
        background-color: $theme-warning-danger;
        border-color: $theme-warning-danger;
        color: $white;
        @include hover-full-state{
            background-color: darken($theme-warning-danger, 10%) !important;
            border-color: darken($theme-warning-danger, 10%) !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-danger, 20%);
            border-color: $theme-warning-danger;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-danger, 20%);
            border-color: $theme-warning-danger;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-danger{
        &.dropdown-toggle{
            background-color: darken($theme-warning-danger, 10%) !important;
            border-color: darken($theme-warning-danger, 10%) !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-danger{
        color: $theme-warning-danger;
        background-color: transparent;
        border-color: $theme-warning-danger !important;        
        @include hover-active-state{
            background-color: darken($theme-warning-danger, 10%) !important;
            border-color: darken($theme-warning-danger, 10%) !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-danger{
        &.dropdown-toggle{
            background-color: darken($theme-warning-danger, 10%) !important;
            border-color: darken($theme-warning-danger, 10%) !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-danger{
        color: $theme-warning-danger !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: darken($theme-warning-danger, 10%) !important;
            border-color: darken($theme-warning-danger, 10%) !important;
            color: $white !important;
        }
    }
    }
}

/*---Warning Button---*/
.theme-warning {
    .btn-warning {
        background-color: $theme-warning-warning;
        border-color: $theme-warning-warning;
        color: $white;
        @include hover-full-state{
            background-color: darken($theme-warning-warning, 10%) !important;
            border-color: darken($theme-warning-warning, 10%) !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-warning, 20%);
            border-color: $theme-warning-warning;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-warning, 20%);
            border-color: $theme-warning-warning;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-warning{
        &.dropdown-toggle{
            background-color: darken($theme-warning-warning, 10%) !important;
            border-color: darken($theme-warning-warning, 10%) !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-warning{
        color: $theme-warning-warning;
        background-color: transparent;
        border-color: $theme-warning-warning !important;        
        @include hover-active-state{
            background-color: darken($theme-warning-warning, 10%) !important;
            border-color: darken($theme-warning-warning, 10%) !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-warning{
        &.dropdown-toggle{
            background-color: darken($theme-warning-warning, 10%) !important;
            border-color: darken($theme-warning-warning, 10%) !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-warning{
        color: $theme-warning-warning !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: darken($theme-warning-warning, 10%) !important;
            border-color: darken($theme-warning-warning, 10%) !important;
            color: $white !important;
        }
    }
    }
}

/*---Primary Button light---*/
.theme-warning {
    .btn-primary-light {
        background-color: $theme-warning-primary-lite;
        border-color: $theme-warning-primary-lite;
        color: $theme-warning-primary;
        @include hover-full-state{
            background-color: $theme-warning-primary !important;
            border-color: $theme-warning-primary !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-primary-lite, 20%);
            border-color: $theme-warning-primary-lite;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-primary-lite, 20%);
            border-color: $theme-warning-primary-lite;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-primary-light{
        &.dropdown-toggle{
            background-color: $theme-warning-primary !important;
            border-color: $theme-warning-primary !important;
            color: $white ;
        }    
        }
        
    }
    .btn-outline{
    &.btn-primary-light{
        color: $theme-warning-primary;
        background-color: transparent;
        border-color: $theme-warning-primary-lite !important;        
        @include hover-active-state{
            background-color: $theme-warning-primary !important;
            border-color: $theme-warning-primary !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-primary-light{
        &.dropdown-toggle{
            background-color: $theme-warning-primary !important;
            border-color: $theme-warning-primary !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-primary-light{
        color: $theme-warning-primary !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: $theme-warning-primary !important;
            border-color: $theme-warning-primary !important;
            color: $white !important;
        }
    }
    }
}

/*---info Button light---*/
.theme-warning {
    .btn-info-light {
        background-color: $theme-warning-info-lite;
        border-color: $theme-warning-info-lite;
        color: $theme-warning-info;
        @include hover-full-state{
            background-color: $theme-warning-info !important;
            border-color: $theme-warning-info !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-info-lite, 20%);
            border-color: $theme-warning-info-lite;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-info-lite, 20%);
            border-color: $theme-warning-info-lite;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-info{
        &.dropdown-toggle{
            background-color: $theme-warning-info !important;
            border-color: $theme-warning-info !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-info-light{
        color: $theme-warning-info;
        background-color: transparent;
        border-color: $theme-warning-info-lite !important;        
        @include hover-active-state{
            background-color: $theme-warning-info !important;
            border-color: $theme-warning-info !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-info-light{
        &.dropdown-toggle{
            background-color: $theme-warning-info !important;
            border-color: $theme-warning-info !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-info-light{
        color: $theme-warning-info !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: $theme-warning-info !important;
            border-color: $theme-warning-info !important;
            color: $white !important;
        }
    }
    }
}

/*---Success Button light---*/
.theme-warning {
    .btn-success-light {
        background-color: $theme-warning-success-lite;
        border-color: $theme-warning-success-lite;
        color: $theme-warning-success;
        @include hover-full-state{
            background-color: $theme-warning-success !important;
            border-color: $theme-warning-success !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-success-lite, 20%);
            border-color: $theme-warning-success-lite;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-success-lite, 20%);
            border-color: $theme-warning-success-lite;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-success-light{
        &.dropdown-toggle{
            background-color: $theme-warning-success !important;
            border-color: $theme-warning-success !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-success-light{
        color: $theme-warning-success;
        background-color: transparent;
        border-color: $theme-warning-success-lite !important;        
        @include hover-active-state{
            background-color: $theme-warning-success !important;
            border-color: $theme-warning-success !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-success-light{
        &.dropdown-toggle{
            background-color: $theme-warning-success !important;
            border-color: $theme-warning-success !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-success-light{
        color: $theme-warning-success !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: $theme-warning-success !important;
            border-color: $theme-warning-success !important;
            color: $white !important;
        }
    }
    }
}


/*---Danger Button light---*/
.theme-warning {
    .btn-danger-light {
        background-color: $theme-warning-danger-lite;
        border-color: $theme-warning-danger-lite;
        color: $theme-warning-danger;
        @include hover-full-state{
            background-color: $theme-warning-danger !important;
            border-color: $theme-warning-danger !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-danger-lite, 20%);
            border-color: $theme-warning-danger-lite;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-danger-lite, 20%);
            border-color: $theme-warning-danger-lite;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-danger-light{
        &.dropdown-toggle{
            background-color: $theme-warning-danger !important;
            border-color: $theme-warning-danger !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-danger-light{
        color: $theme-warning-danger;
        background-color: transparent;
        border-color: $theme-warning-danger-lite !important;        
        @include hover-active-state{
            background-color: $theme-warning-danger !important;
            border-color: $theme-warning-danger !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-danger-light{
        &.dropdown-toggle{
            background-color: $theme-warning-danger !important;
            border-color: $theme-warning-danger !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-danger-light{
        color: $theme-warning-danger !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: $theme-warning-danger !important;
            border-color: $theme-warning-danger !important;
            color: $white !important;
        }
    }
    }
}

/*---Warning Button light---*/
.theme-warning {
    .btn-warning-light {
        background-color: $theme-warning-warning-lite;
        border-color: $theme-warning-warning-lite;
        color: $theme-warning-warning;
        @include hover-full-state{
            background-color: $theme-warning-warning !important;
            border-color: $theme-warning-warning !important;
            color: $white !important;
        } 
        &:disabled {
            background-color: lighten($theme-warning-warning-lite, 20%);
            border-color: $theme-warning-warning-lite;
            opacity: 0.5;
        }
        &.disabled {
            background-color: lighten($theme-warning-warning-lite, 20%);
            border-color: $theme-warning-warning-lite;
            opacity: 0.5;
        }
    }
    .show > {
        .btn-warning-light{
        &.dropdown-toggle{
            background-color: $theme-warning-warning !important;
            border-color: $theme-warning-warning !important;
            color: $white;
        }    
        }
        
    }
    .btn-outline{
    &.btn-warning-light{
        color: $theme-warning-warning;
        background-color: transparent;
        border-color: $theme-warning-warning-lite !important;        
        @include hover-active-state{
            background-color: $theme-warning-warning !important;
            border-color: $theme-warning-warning !important;
            color: $white !important;
        }
    }
    }
    .show > {
        .btn-outline{
        &.btn-warning-light{
        &.dropdown-toggle{
            background-color: $theme-warning-warning !important;
            border-color: $theme-warning-warning !important;
            color: $white;
        }    
        }
        }
    }
    .btn-flat{
    &.btn-warning-light{
        color: $theme-warning-warning !important;
        background-color: $light3;
        border-color: transparent;
        @include hover-active-state{
            background-color: $theme-warning-warning !important;
            border-color: $theme-warning-warning !important;
            color: $white !important;
        }
    }
    }
}

    /*---callout---*/
.theme-warning{
    .callout{
    &.callout-primary {
        border-color: $theme-warning-primary;
        background-color: $theme-warning-primary !important;
    }
        
    &.callout-info {
        border-color: $theme-warning-info;
        background-color: $theme-warning-info !important;
    }
        
    &.callout-success {
        border-color: $theme-warning-success;
        background-color: $theme-warning-success !important;
    }
        
    &.callout-danger {
        border-color: $theme-warning-danger;
        background-color: $theme-warning-danger !important;
    }
        
    &.callout-warning {
        border-color: $theme-warning-warning;
        background-color: $theme-warning-warning !important;
    }
    }
}

    /*---alert---*/
.theme-warning{
    .alert-primary{
        border-color: $theme-warning-primary;
        background-color: $theme-warning-primary !important;
        color: $white;
    }
    .alert-info{
        border-color: $theme-warning-info;
        background-color: $theme-warning-info !important;
        color: $white;
    }
    .alert-success{
        border-color: $theme-warning-success;
        background-color: $theme-warning-success !important;
        color: $white;
    }
    .alert-danger{
        border-color: $theme-warning-danger;
        background-color: $theme-warning-danger !important;
        color: $white;
    }
    .alert-error{
        border-color: $theme-warning-danger;
        background-color: $theme-warning-danger !important;
        color: $white;
    }
    .alert-warning{
        border-color: $theme-warning-warning;
        background-color: $theme-warning-warning !important;
        color: $white;
    }
}

    /*---direct-chat---*/
.theme-warning {
    .direct-chat-primary {
        .right > {
            .direct-chat-text {
                p {
                    background-color: $theme-warning-primary;
                    color: $white;
                }
                @include before-after-state{
                    border-left-color: $theme-warning-primary;
                }
            }
        }
    }
    .direct-chat-info {
        .right > {
            .direct-chat-text {
                p {
                    background-color: $theme-warning-info;
                    color: $white;
                }
                @include before-after-state{
                    border-left-color: $theme-warning-info;
                }
            }
        }
    }
    .direct-chat-success {
        .right > {
            .direct-chat-text {
                p {
                    background-color: $theme-warning-success;
                    color: $white;
                }
                @include before-after-state{
                    border-left-color: $theme-warning-success;
                }
            }
        }
    }
    .direct-chat-danger {
        .right > {
            .direct-chat-text {
                p {
                    background-color: $theme-warning-danger;
                    color: $white;
                }
                @include before-after-state{
                    border-left-color: $theme-warning-danger;
                }
            }
        }
    }
    .direct-chat-warning {
        .right > {
            .direct-chat-text {
                p {
                    background-color: $theme-warning-warning;
                    color: $white;
                }
                @include before-after-state{
                    border-left-color: $theme-warning-warning;
                }
            }
        }
    }
    .right{
        .direct-chat-text {
            p {
                background-color: $theme-warning-primary;
            }
        }
    }
}

    /*---modal---*/
.theme-warning{
    .modal-primary {
        .modal-footer{
            border-color: $theme-warning-primary;
        }
        .modal-header{
            @extend .modal-footer 
        }
        .modal-body{
            background-color: $theme-warning-primary !important;
        }
    }
    .modal-info {
        .modal-footer{
            border-color: $theme-warning-info;
        }
        .modal-header{
            @extend .modal-footer 
        }
        .modal-body{
            background-color: $theme-warning-info !important;
        }
    }
    .modal-success {
        .modal-footer{
            border-color: $theme-warning-success;
        }
        .modal-header{
            @extend .modal-footer 
        }
        .modal-body{
            background-color: $theme-warning-success !important;
        }
    }
    .modal-danger {
        .modal-footer{
            border-color: $theme-warning-danger;
        }
        .modal-header{
            @extend .modal-footer 
        }
        .modal-body{
            background-color: $theme-warning-danger !important;
        }
    }
    .modal-warning {
        .modal-footer{
            border-color: $theme-warning-warning;
        }
        .modal-header{
            @extend .modal-footer 
        }
        .modal-body{
            background-color: $theme-warning-warning !important;
        }
    }
}

    /*---border---*/
.theme-warning {
    .border-primary {
        border-color: $theme-warning-primary !important;
    }
    .border-info {
        border-color: $theme-warning-info !important;
    }
    .border-success {
        border-color: $theme-warning-success !important;
    }
    .border-danger {
        border-color: $theme-warning-danger !important;
    }
    .border-warning {
        border-color: $theme-warning-warning !important;
    }
}

    /*---Background---*/
.theme-warning {
    .bg-primary {
      background-color: $theme-warning-primary !important;
      color: $white;
    }
    .bg-primary-light {
      background-color: $theme-warning-primary-lite !important;
      color: $theme-warning-primary;
    }
    .bg-info {
      background-color: $theme-warning-info !important;
      color: $white;
    }
    .bg-info-light {
      background-color: $theme-warning-info-lite !important;
      color: $theme-warning-info;
    }
    .bg-success {
      background-color: $theme-warning-success !important;
      color: $white;
    }
    .bg-success-light {
      background-color: $theme-warning-success-lite !important;
      color: $theme-warning-success;
    }
    .bg-danger {
      background-color: $theme-warning-danger !important;
      color: $white;
    }
    .bg-danger-light {
      background-color: $theme-warning-danger-lite !important;
      color: $theme-warning-danger;
    }
    .bg-warning {
      background-color: $theme-warning-warning !important;
      color: $white;
    }
    .bg-warning-light {
      background-color: $theme-warning-warning-lite !important;
      color: $theme-warning-warning;
    }
}

    /*---text---*/
.theme-warning {
    .text-primary {
      color: $theme-warning-primary !important;
    }
    a{
    &.text-primary{
        @include hover-focus-state{
            color: $theme-warning-primary !important;    
        }
    }
    }
    .hover-primary{
        @include hover-focus-state{
            color: $theme-warning-primary !important;    
        }
    }
    
    .text-info {
      color: $theme-warning-info !important;
    }
    a{
    &.text-info{
        @include hover-focus-state{
            color: $theme-warning-info !important;    
        }
    }
    }
    .hover-info{
        @include hover-focus-state{
            color: $theme-warning-info !important;    
        }
    }
    
    .text-success {
      color: $theme-warning-success !important;
    }
    a{
    &.text-success{
        @include hover-focus-state{
            color: $theme-warning-success !important;    
        }
    }
    }
    .hover-success{
        @include hover-focus-state{
            color: $theme-warning-success !important;    
        }
    }
    
    .text-danger {
      color: $theme-warning-danger !important;
    }
    a{
    &.text-danger{
        @include hover-focus-state{
            color: $theme-warning-danger !important;    
        }
    }
    }
    .hover-danger{
        @include hover-focus-state{
            color: $theme-warning-danger !important;    
        }
    }
    
    .text-warning {
      color: $theme-warning-warning !important;
    }
    a{
    &.text-warning{
        @include hover-focus-state{
            color: $theme-warning-warning !important;    
        }
    }
    }
    .hover-warning{
        @include hover-focus-state{
            color: $theme-warning-warning !important;    
        }
    }
}

    /*---active background---*/
.theme-warning {
    .active{
    &.active-primary {
        background-color: darken($theme-warning-primary, 10%) !important;
    }
    &.active-info {
        background-color: darken($theme-warning-info, 10%) !important;
    }
    &.active-success {
        background-color: darken($theme-warning-success, 10%) !important;
    }
    &.active-danger {
        background-color: darken($theme-warning-danger, 10%) !important;
    }
    &.active-warning {
        background-color: darken($theme-warning-warning, 10%) !important;
    }
    }
}

    /*---label background---*/
.theme-warning {
    .label-primary{
        background-color: $theme-warning-primary !important;
    }
    .label-info{
        background-color: $theme-warning-info !important;
    }
    .label-success{
        background-color: $theme-warning-success !important;
    }
    .label-danger{
        background-color: $theme-warning-danger !important;
    }
    .label-warning{
        background-color: $theme-warning-warning !important;
    }
}

    /*---ribbon---*/

$ribbon-bod-w: 3px;
$ribbon-bod-s: solid;

.theme-warning {
    .ribbon-box {
        .ribbon-primary {
            background-color: $theme-warning-primary;
            
            &:before  {
                border-color: $theme-warning-primary transparent transparent;
            }
        }
        .ribbon-two-primary{
            span{
                background-color: $theme-warning-primary; 
            &:before{
                border-left: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-primary, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-primary, 10%);    
            }
            &:after{
                border-right: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-primary, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-primary, 10%);    
            }
            }
        }
        
        .ribbon-info {
            background-color: $theme-warning-info;
            
            &:before  {
                border-color: $theme-warning-info transparent transparent;
            }
        }
        .ribbon-two-info{
            span{
                background-color: $theme-warning-info; 
            &:before{
                border-left: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-info, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-info, 10%);    
            }
            &:after{
                border-right: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-info, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-info, 10%);    
            }
            }
        }
        
        .ribbon-success {
            background-color: $theme-warning-success;
            
            &:before  {
                border-color: $theme-warning-success transparent transparent;
            }
        }
        .ribbon-two-success{
            span{
                background-color: $theme-warning-success; 
            &:before{
                border-left: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-success, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-success, 10%);    
            }
            &:after{
                border-right: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-success, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-success, 10%);    
            }
            }
        }
        
        .ribbon-danger {
            background-color: $theme-warning-danger;
            
            &:before  {
                border-color: $theme-warning-danger transparent transparent;
            }
        }
        .ribbon-two-danger{
            span{
                background-color: $theme-warning-danger; 
            &:before{
                border-left: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-danger, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-danger, 10%);    
            }
            &:after{
                border-right: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-danger, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-danger, 10%);    
            }
            }
        }
        
        .ribbon-warning {
            background-color: $theme-warning-warning;
            
            &:before  {
                border-color: $theme-warning-warning transparent transparent;
            }
        }
        .ribbon-two-warning{
            span{
                background-color: $theme-warning-warning; 
            &:before{
                border-left: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-warning, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-warning, 10%);    
            }
            &:after{
                border-right: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-warning, 10%);  
                border-top: $ribbon-bod-w $ribbon-bod-s darken($theme-warning-warning, 10%);    
            }
            }
        }
    }
}

    /*---Box---*/
$box-bod-w: 1px;
$box-bod-s: solid;

.theme-warning{ 
    .box-primary {
        background-color: $theme-warning-primary !important;
    &.box-bordered{
        border-color: $theme-warning-primary;
    }
    }
    .box-outline-primary {
        background-color: $white;
        border: $box-bod-w $box-bod-s $theme-warning-primary;
    }
    .box{
    &.box-solid{
    &.box-primary > {
        .box-header {
            color: $white;
            background-color: $theme-warning-primary;
            .btn{
                color: $white;    
            }
            > a{
                color: $white;                  
            }
        }
    }
    }
    }
     
    .box-info {
        background-color: $theme-warning-info !important;
    &.box-bordered{
        border-color: $theme-warning-info;
    }
    }
    .box-outline-info {
        background-color: $white;
        border: $box-bod-w $box-bod-s $theme-warning-info;
    }
    .box{
    &.box-solid{
    &.box-info > {
        .box-header {
            color: $white;
            background-color: $theme-warning-info;
            .btn{
                color: $white;    
            }
            > a{
                color: $white;                  
            }
        }
    }
    }
    }
     
    .box-success {
        background-color: $theme-warning-success !important;
    &.box-bordered{
        border-color: $theme-warning-success;
    }
    }
    .box-outline-success {
        background-color: $white;
        border: $box-bod-w $box-bod-s $theme-warning-success;
    }
    .box{
    &.box-solid{
    &.box-success > {
        .box-header {
            color: $white;
            background-color: $theme-warning-success;
            .btn{
                color: $white;    
            }
            > a{
                color: $white;                  
            }
        }
    }
    }
    }
     
    .box-danger {
        background-color: $theme-warning-danger !important;
    &.box-bordered{
        border-color: $theme-warning-danger;
    }
    }
    .box-outline-danger {
        background-color: $white;
        border: $box-bod-w $box-bod-s $theme-warning-danger;
    }
    .box{
    &.box-solid{
    &.box-danger > {
        .box-header {
            color: $white;
            background-color: $theme-warning-danger;
            .btn{
                color: $white;    
            }
            > a{
                color: $white;                  
            }
        }
    }
    }
    }
     
    .box-warning {
        background-color: $theme-warning-warning !important;
    &.box-bordered{
        border-color: $theme-warning-warning;
    }
    }
    .box-outline-warning {
        background-color: $white;
        border: $box-bod-w $box-bod-s $theme-warning-warning;
    }
    .box{
    &.box-solid{
    &.box-warning > {
        .box-header {
            color: $white;
            background-color: $theme-warning-warning;
            .btn{
                color: $white;    
            }
            > a{
                color: $white;                  
            }
        }
    }
    }
    }
    
    
    .box-profile {
        .social-states {
            a{
            &:hover {
                color: darken($theme-warning-primary, 10%);
            }
            }
        }
    }
    .box-controls {
        li > {
            a{
            &:hover {
                color: darken($theme-warning-primary, 10%);
            }
            }
        }
        .dropdown {
        &.show > {
            a {
                color: darken($theme-warning-primary, 10%);
            }
        }
        }
    }
    .box-fullscreen {
        .box-btn-fullscreen {
            color: darken($theme-warning-primary, 10%);
        }
    }
}

    /*---progress bar---*/
.theme-warning {
    .progress-bar-primary {
        background-color: $theme-warning-primary;
    }
    .progress-bar-info {
        background-color: $theme-warning-info;
    }
    .progress-bar-success {
        background-color: $theme-warning-success;
    }
    .progress-bar-danger {
        background-color: $theme-warning-danger;
    }
    .progress-bar-warning {
        background-color: $theme-warning-warning;
    }
}
    /*---panel---*/
.theme-warning {
    .panel-primary {
        border-color: $theme-warning-primary;
        > .panel-heading {
            color: $white;
            background-color: $theme-warning-primary;
            border-color: $theme-warning-primary;
            + .panel-collapse {
                > .panel-body {
                  border-top-color: $theme-warning-primary;
                }
            }
            .badge-pill {
                color: $theme-warning-primary;
                background-color: $white;
            }
        }
        .panel-title {
            color: $white;
        }
        .panel-action {
            @extend .panel-title
        }
        .panel-footer {
            + .panel-collapse {
                > .panel-body {
                  border-bottom-color: $theme-warning-primary;
                }
            }
        }
    }
    .panel-line{
    &.panel-primary {
        .panel-heading {
          color: $theme-warning-primary;
          border-top-color: $theme-warning-primary;
          background: transparent;
        }
        .panel-title {
            color: $theme-warning-primary;            
        }
    }
    }    
    
    .panel-info {
        border-color: $theme-warning-info;
        > .panel-heading {
            color: $white;
            background-color: $theme-warning-info;
            border-color: $theme-warning-info;
            + .panel-collapse {
                > .panel-body {
                  border-top-color: $theme-warning-info;
                }
            }
            .badge-pill {
                color: $theme-warning-info;
                background-color: $white;
            }
        }
        .panel-title {
            color: $white;
        }
        .panel-action {
            @extend .panel-title
        }
        .panel-footer {
            + .panel-collapse {
                > .panel-body {
                  border-bottom-color: $theme-warning-info;
                }
            }
        }
    }
    .panel-line{
    &.panel-info {
        .panel-heading {
          color: $theme-warning-info;
          border-top-color: $theme-warning-info;
          background: transparent;
        }
        .panel-title {
            color: $theme-warning-info;            
        }
    }
    }    
    
    .panel-success {
        border-color: $theme-warning-success;
        > .panel-heading {
            color: $white;
            background-color: $theme-warning-success;
            border-color: $theme-warning-success;
            + .panel-collapse {
                > .panel-body {
                  border-top-color: $theme-warning-success;
                }
            }
            .badge-pill {
                color: $theme-warning-success;
                background-color: $white;
            }
        }
        .panel-title {
            color: $white;
        }
        .panel-action {
            @extend .panel-title
        }
        .panel-footer {
            + .panel-collapse {
                > .panel-body {
                  border-bottom-color: $theme-warning-success;
                }
            }
        }
    }
    .panel-line{
    &.panel-success {
        .panel-heading {
          color: $theme-warning-success;
          border-top-color: $theme-warning-success;
          background: transparent;
        }
        .panel-title {
            color: $theme-warning-success;            
        }
    }
    }    
    
    .panel-danger {
        border-color: $theme-warning-danger;
        > .panel-heading {
            color: $white;
            background-color: $theme-warning-danger;
            border-color: $theme-warning-danger;
            + .panel-collapse {
                > .panel-body {
                  border-top-color: $theme-warning-danger;
                }
            }
            .badge-pill {
                color: $theme-warning-danger;
                background-color: $white;
            }
        }
        .panel-title {
            color: $white;
        }
        .panel-action {
            @extend .panel-title
        }
        .panel-footer {
            + .panel-collapse {
                > .panel-body {
                  border-bottom-color: $theme-warning-danger;
                }
            }
        }
    }
    .panel-line{
    &.panel-danger {
        .panel-heading {
          color: $theme-warning-danger;
          border-top-color: $theme-warning-danger;
          background: transparent;
        }
        .panel-title {
            color: $theme-warning-danger;            
        }
    }
    }    
    
    .panel-warning {
        border-color: $theme-warning-warning;
        > .panel-heading {
            color: $white;
            background-color: $theme-warning-warning;
            border-color: $theme-warning-warning;
            + .panel-collapse {
                > .panel-body {
                  border-top-color: $theme-warning-warning;
                }
            }
            .badge-pill {
                color: $theme-warning-warning;
                background-color: $white;
            }
        }
        .panel-title {
            color: $white;
        }
        .panel-action {
            @extend .panel-title
        }
        .panel-footer {
            + .panel-collapse {
                > .panel-body {
                  border-bottom-color: $theme-warning-warning;
                }
            }
        }
    }
    .panel-line{
    &.panel-warning {
        .panel-heading {
          color: $theme-warning-warning;
          border-top-color: $theme-warning-warning;
          background: transparent;
        }
        .panel-title {
            color: $theme-warning-warning;            
        }
    }
    }
    
}

    /*---switch---*/
.theme-warning {
    .switch{    
    input {
    &:checked {
        ~ .switch-indicator{
          &::after {
            background-color: $theme-warning-primary;
          }
        }
    }
    }
    &.switch-primary {
        input {
        &:checked {
            ~ .switch-indicator{
              &::after {
                background-color: $theme-warning-primary;
              }
            }
        }
        }
    }
    &.switch-info {
        input {
        &:checked {
            ~ .switch-indicator{
              &::after {
                background-color: $theme-warning-info;
              }
            }
        }
        }
    }
    &.switch-success {
        input {
        &:checked {
            ~ .switch-indicator{
              &::after {
                background-color: $theme-warning-success;
              }
            }
        }
        }
    }
    &.switch-danger {
        input {
        &:checked {
            ~ .switch-indicator{
              &::after {
                background-color: $theme-warning-danger;
              }
            }
        }
        }
    }
    &.switch-warning {
        input {
        &:checked {
            ~ .switch-indicator{
              &::after {
                background-color: $theme-warning-warning;
              }
            }
        }
        }
    }
    }
}

    /*---badge---*/
.theme-warning {
    .badge-primary {
        background-color: $theme-warning-primary;
        color: $white;
    }
    .badge-primary[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-primary, 10%);
        }
    }
    .badge-secondary {
        background-color: $theme-warning-secondary;
        color: $dark;
    }
    .badge-secondary[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-secondary, 10%);
        }
    }
    .badge-info {
        background-color: $theme-warning-info;
        color: $white;
    }
    .badge-info[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-info, 10%);
        }
    }
    .badge-success {
        background-color: $theme-warning-success;
        color: $white;
    }
    .badge-success[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-success, 10%);
        }
    }
    .badge-danger {
        background-color: $theme-warning-danger;
        color: $white;
    }
    .badge-danger[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-danger, 10%);
        }
    }
    .badge-warning {
        background-color: $theme-warning-warning;
        color: $white;
    }
    .badge-warning[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-warning, 10%);
        }
    }
}

    /*---badge light---*/
.theme-warning {
    .badge-primary-light {
        background-color: $theme-warning-primary-lite;
        color: $theme-warning-primary;
    }
    .badge-primary-light[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-primary-lite, 10%);
        }
    }
    .badge-secondary-light {
        background-color: $theme-warning-secondary-lite;
        color: $dark;
    }
    .badge-secondary-light[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-secondary-lite, 10%);
        }
    }
    .badge-info-light {
        background-color: $theme-warning-info-lite;
        color: $theme-warning-info;
    }
    .badge-info-light[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-info-lite, 10%);
        }
    }
    .badge-success-light {
        background-color: $theme-warning-success-lite;
        color: $theme-warning-success;
    }
    .badge-success-light[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-success-lite, 10%);
        }
    }
    .badge-danger-light {
        background-color: $theme-warning-danger-lite;
        color: $theme-warning-danger;
    }
    .badge-danger-light[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-danger-lite, 10%);
        }
    }
    .badge-warning-light {
        background-color: $theme-warning-warning-lite;
        color: $theme-warning-warning;
    }
    .badge-warning-light[href]{
        @include hover-focus-state{
            background-color: darken($theme-warning-warning-lite, 10%);
        }
    }
}

    /*---rating---*/
.theme-warning {
    .rating-primary {
        .active {
            color: $theme-warning-primary;
        }
        :checked ~ label {
            color: $theme-warning-primary;
        }
        label{
            &:hover {
                color: $theme-warning-primary;
                ~ label {
                    color: $theme-warning-primary;
                }
            }
        }
    }
    .rating-info {
        .active {
            color: $theme-warning-info;
        }
        :checked ~ label {
            color: $theme-warning-info;
        }
        label{
            &:hover {
                color: $theme-warning-info;
                ~ label {
                    color: $theme-warning-info;
                }
            }
        }
    }
    .rating-success {
        .active {
            color: $theme-warning-success;
        }
        :checked ~ label {
            color: $theme-warning-success;
        }
        label{
            &:hover {
                color: $theme-warning-success;
                ~ label {
                    color: $theme-warning-success;
                }
            }
        }
    }
    .rating-danger {
        .active {
            color: $theme-warning-danger;
        }
        :checked ~ label {
            color: $theme-warning-danger;
        }
        label{
            &:hover {
                color: $theme-warning-danger;
                ~ label {
                    color: $theme-warning-danger;
                }
            }
        }
    }
    .rating-warning {
        .active {
            color: $theme-warning-warning;
        }
        :checked ~ label {
            color: $theme-warning-warning;
        }
        label{
            &:hover {
                color: $theme-warning-warning;
                ~ label {
                    color: $theme-warning-warning;
                }
            }
        }
    }
}

    /*---toggler---*/
.theme-warning {
    .toggler-primary {
        input{
        &:checked + i {
            color: $theme-warning-primary;
        }
        }
    }
    .toggler-info {
        input{
        &:checked + i {
            color: $theme-warning-info;
        }
        }
    }
    .toggler-success {
        input{
        &:checked + i {
            color: $theme-warning-success;
        }
        }
    }
    .toggler-danger {
        input{
        &:checked + i {
            color: $theme-warning-danger;
        }
        }
    }
    .toggler-warning {
        input{
        &:checked + i {
            color: $theme-warning-warning;
        }
        }
    }
}

    /*---nav tabs---*/
.theme-warning {
    .nav-tabs{
    &.nav-tabs-primary {
        .nav-link{
            @include hover-full-state{
                border-color: darken($theme-warning-primary, 10%);
                background-color: transparent;
                color: darken($theme-warning-primary, 10%);
            }
        }
    }
    &.nav-tabs-info {
        .nav-link{
            @include hover-full-state{
                border-color: darken($theme-warning-info, 10%);
                background-color: $theme-warning-info;
                color: $white;
            }
        }
    }
    &.nav-tabs-success {
        .nav-link{
            @include hover-full-state{
                border-color: darken($theme-warning-success, 10%);
                background-color: transparent;
                color: darken($theme-warning-success, 10%);
            }
        }
    }
    &.nav-tabs-danger {
        .nav-link{
            @include hover-full-state{
                border-color: darken($theme-warning-danger, 10%);
                background-color: transparent;
                color: darken($theme-warning-danger, 10%);
            }
        }
    }
    &.nav-tabs-warning {
        .nav-link{
            @include hover-full-state{
                border-color: darken($theme-warning-warning, 10%);
                background-color: transparent;
                color: darken($theme-warning-warning, 10%);
            }
        }
    }
    }
    .nav-tabs-custom{
    &.tab-primary{
        >.nav-tabs{
            >li {
                a{
                &.active {
                    border-top-color: darken($theme-warning-primary, 10%);
                }
                }
            }
        }
    }
    &.tab-info{
        >.nav-tabs{
            >li {
                a{
                &.active {
                    border-top-color: darken($theme-warning-info, 10%);
                }
                }
            }
        }
    }
    &.tab-success{
        >.nav-tabs{
            >li {
                a{
                &.active {
                    border-top-color: darken($theme-warning-success, 10%);
                }
                }
            }
        }
    }
    &.tab-danger{
        >.nav-tabs{
            >li {
                a{
                &.active {
                    border-top-color: darken($theme-warning-danger, 10%);
                }
                }
            }
        }
    }
    &.tab-warning{
        >.nav-tabs{
            >li {
                a{
                &.active {
                    border-top-color: darken($theme-warning-warning, 10%);
                }
                }
            }
        }
    }
    }
    .nav-tabs {
        .nav-link{
        &.active{
            border-bottom-color: $theme-warning-primary;
            background-color: $theme-warning-primary;
            color: $white;
            @include hover-focus-state{
                border-bottom-color: $theme-warning-primary;
                background-color: $theme-warning-primary;
                color: $white;
            }
        }
        } 
        .nav-item{
        &.open{
            .nav-link{
                border-bottom-color: $theme-warning-primary;
                background-color: $theme-warning-primary;
                @include hover-focus-state{
                    border-bottom-color: $theme-warning-primary;
                    background-color: $theme-warning-primary;    
                }
            }
        }
        }
    }
}

    /*---todo---*/
.theme-warning {
    .todo-list {
        .primary {
            border-left-color: $theme-warning-primary;
        }
        .info {
            border-left-color: $theme-warning-primary;
        }
        .success {
            border-left-color: $theme-warning-success;
        }
        .danger {
            border-left-color: $theme-warning-danger;
        }
        .warning {
            border-left-color: $theme-warning-warning;
        }
    }
}

    /*---timeline---*/
.theme-warning {
    .timeline {
        .timeline-item {
            > .timeline-event{
                &.timeline-event-primary {
                  background-color: $theme-warning-primary;
                  border: 1px solid  $theme-warning-primary;
                  color: $white;
                @include before-after-state {
                  border-left-color: $theme-warning-primary;
                  border-right-color: $theme-warning-primary;
                }
                * {
                  color: inherit;
                }
                }
                &.timeline-event-info {
                  background-color: $theme-warning-info;
                  border: 1px solid  $theme-warning-info;
                  color: $white;
                @include before-after-state {
                  border-left-color: $theme-warning-info;
                  border-right-color: $theme-warning-info;
                }
                * {
                  color: inherit;
                }
                }
                &.timeline-event-success {
                  background-color: $theme-warning-success;
                  border: 1px solid  $theme-warning-success;
                  color: $white;
                @include before-after-state {
                  border-left-color: $theme-warning-success;
                  border-right-color: $theme-warning-success;
                }
                * {
                  color: inherit;
                }
                }
                &.timeline-event-danger {
                  background-color: $theme-warning-danger;
                  border: 1px solid  $theme-warning-danger;
                  color: $white;
                @include before-after-state {
                  border-left-color: $theme-warning-danger;
                  border-right-color: $theme-warning-danger;
                }
                * {
                  color: inherit;
                }
                }
                &.timeline-event-warning {
                  background-color: $theme-warning-warning;
                  border: 1px solid  $theme-warning-warning;
                  color: $white;
                @include before-after-state {
                  border-left-color: $theme-warning-warning;
                  border-right-color: $theme-warning-warning;
                }
                * {
                  color: inherit;
                }
                }
            }
            > .timeline-point{
                &.timeline-point-primary {
                  color: $theme-warning-primary;
                  background-color: $white;
                }
                &.timeline-point-info {
                  color: $theme-warning-info;
                  background-color: $white;
                }
                &.timeline-point-success {
                  color: $theme-warning-success;
                  background-color: $white;
                }
                &.timeline-point-danger {
                  color: $theme-warning-danger;
                  background-color: $white;
                }
                &.timeline-point-warning {
                  color: $theme-warning-warning;
                  background-color: $white;
                }
            }
        }
        .timeline-label {
            .label-primary {
                background-color: $theme-warning-primary;
            }
            .label-info {
                background-color: $theme-warning-info;
            }
            .label-success {
                background-color: $theme-warning-success;
            }
            .label-danger {
                background-color: $theme-warning-danger;
            }
            .label-warning {
                background-color: $theme-warning-warning;
            }
        }
    }
    
    .timeline__year{
        background-color: $theme-warning-primary;
    }
    .timeline5:before{
        @extend .timeline__year
    }
    .timeline__box:before{
        @extend .timeline__year
    }
    .timeline__date{
        @extend .timeline__year
    }
    .timeline__post{
        border-left: 3px solid $theme-warning-primary;
    }
}

    /*---daterangepicker---*/
.theme-warning{
    .daterangepicker{
        td{
            &.active{
                background-color: $theme-warning-primary; 
                &:hover{
                   background-color: $theme-warning-primary; 
                }
            }
        }
        .input-mini.active {
            border: 1px solid $theme-warning-primary;
        }
    }
    .ranges {
        li{
            @include hover-active-state{
                border: 1px solid $theme-warning-primary;
                background-color: $theme-warning-primary; 
            }
        }
    }
}

    /*---control-sidebar---*/
.theme-warning{
    .control-sidebar{
        .nav-tabs.control-sidebar-tabs{
            >li{
                >a{
                    @include hover-state{
                        border-color: $theme-warning-primary;
                        color: $theme-warning-primary;
                    }
                    &.active{                        
                        border-color: $theme-warning-primary;
                        color: $theme-warning-primary;
                        @include hover-state{
                            border-color: $theme-warning-primary;
                            color: $theme-warning-primary;
                        }
                    }
                }
            }
        }
        .rpanel-title {
            .btn:hover {
                color: $theme-warning-primary;
            }
        }
    }
}

    /*---nav---*/
.theme-warning{
    .nav{
        >li{
            >a{
                @include hover-state{
                   color: $theme-warning-primary; 
                } 
            }
        }
    }
    .nav-pills{
        >li{
            >a{ 
                &.active{
                       border-top-color: $theme-warning-primary;
	                   background-color: $theme-warning-primary !important;
                       color: $white;
                    @include hover-focus-state{
                       border-top-color: $theme-warning-primary;
	                   background-color: $theme-warning-primary !important;
                       color: $white;
                    }     
                }
            }
        }
    }
    .mailbox-nav{
        .nav-pills{
            >li{
                >a{ 
                    @include hover-focus-state{
                       border-color: $theme-warning-primary;
                    }     
                    &.active{
                           border-color: $theme-warning-primary;
                        @include hover-focus-state{
                           border-color: $theme-warning-primary;
                        }     
                    }
                }
            }
        }
    }  
    .nav-tabs-custom{
        >.nav-tabs{
            >li{
                a{      
                    &.active{
                        border-top-color: $theme-warning-primary;    
                    }
                }
            }
        }
    }
    .profile-tab{
        li{
            a{
                &.nav-link{      
                    &.active{
                        border-bottom: 2px solid $theme-warning-primary;    
                    }
                }
            }
        }
    }
    .customtab{
        li{
            a{
                &.nav-link{      
                    &.active{
                        border-bottom: 2px solid $theme-warning-primary;    
                    }
                }
            }
        }
    }
}

    /*---form-element---*/
.theme-warning {
    .form-element {
        .input-group {
            .input-group-addon{
                background-image: $theme-warning-grd, linear-gradient(lighten($dark, 30%), lighten($dark, 30%),) 
            }
        }
        .form-control {            
            &:focus {
                background-image: $theme-warning-grd, linear-gradient(lighten($dark, 30%), lighten($dark, 30%),) 
            }            
            background-image: $theme-warning-grd, linear-gradient(lighten($dark, 30%), lighten($dark, 30%),) 
        }
    }
    .form-control {            
        &:focus {
            border-color: $theme-warning-primary;
        }            
    }
    [type=checkbox]:checked {
        &.chk-col-primary {
            &+label {
                &:before {
                    border-right: 2px solid $theme-warning-primary;
                    border-bottom: 2px solid $theme-warning-primary;
                }
            }
        }
        &.chk-col-info {
            &+label {
                &:before {
                    border-right: 2px solid $theme-warning-info;
                    border-bottom: 2px solid $theme-warning-info;
                }
            }
        }
        &.chk-col-success {
            &+label {
                &:before {
                    border-right: 2px solid $theme-warning-success;
                    border-bottom: 2px solid $theme-warning-success;
                }
            }
        }
        &.chk-col-danger {
            &+label {
                &:before {
                    border-right: 2px solid $theme-warning-danger;
                    border-bottom: 2px solid $theme-warning-danger;
                }
            }
        }
        &.chk-col-warning {
            &+label {
                &:before {
                    border-right: 2px solid $theme-warning-warning;
                    border-bottom: 2px solid $theme-warning-warning;
                }
            }
        }
    }
    [type=checkbox].filled-in:checked {
        &.chk-col-primary {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-primary;
                    background-color: $theme-warning-primary;
                }
            }
        }
        &.chk-col-info {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-info;
                    background-color: $theme-warning-info;
                }
            }
        }
        &.chk-col-success {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-success;
                    background-color: $theme-warning-success;
                }
            }
        }
        &.chk-col-danger {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-danger;
                    background-color: $theme-warning-danger;
                }
            }
        }
        &.chk-col-warning {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-warning;
                    background-color: $theme-warning-warning;
                }
            }
        }
    }
    [type=radio].radio-col-primary {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-primary;
                    border-color: $theme-warning-primary;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-primary {
        &:checked {
            &+label {
                &:before {
                    border: 2px solid $theme-warning-primary;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-primary {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-primary;
                    border: 2px solid $theme-warning-primary;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].radio-col-info {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-info;
                    border-color: $theme-warning-info;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-info {
        &:checked {
            &+label {
                &:before {
                    border: 2px solid $theme-warning-info;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-info {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-info;
                    border: 2px solid $theme-warning-info;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].radio-col-success {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-success;
                    border-color: $theme-warning-success;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-success {
        &:checked {
            &+label {
                &:before {
                    border: 2px solid $theme-warning-success;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-success {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-success;
                    border: 2px solid $theme-warning-success;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].radio-col-danger {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-danger;
                    border-color: $theme-warning-danger;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-danger {
        &:checked {
            &+label {
                &:before {
                    border: 2px solid $theme-warning-danger;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-danger {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-danger;
                    border: 2px solid $theme-warning-danger;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].radio-col-warning {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-warning;
                    border-color: $theme-warning-warning;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-warning {
        &:checked {
            &+label {
                &:before {
                    border: 2px solid $theme-warning-warning;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    [type=radio].with-gap.radio-col-warning {
        &:checked {
            &+label {
                &:after {
                    background-color: $theme-warning-warning;
                    border: 2px solid $theme-warning-warning;
                    -webkit-animation: ripple .2s linear forwards;
                    animation: ripple .2s linear forwards;
                }
            }
        }
    }
    
    [type=checkbox]{
        &:checked {
            &+label {
                &:before {
                    border-right: 2px solid $theme-warning-primary;
                    border-bottom: 2px solid $theme-warning-primary;
                }
            }
        }
    }
    [type=checkbox].filled-in{
        &:checked {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-primary;
                    background-color: $theme-warning-primary;
                }
            }
        }
    }
    [type=radio]{
        &.with-gap{
        &:checked {
            &+label {
                @include before-after-state{
                    border: 2px solid $theme-warning-primary;
                }
                &:after {
                    background-color: $theme-warning-primary;
                    z-index: 0;
                }
            }
        }
        }        
        &:checked {
            &+label {
                &:after {
                    border: 2px solid $theme-warning-primary;
                    background-color: $theme-warning-primary;
                    z-index: 0;
                }
            }
        }
    }
    [type=checkbox].filled-in.tabbed{
        &:checked:focus {
            &+label {
                &:after {
                    border-color: $theme-warning-primary;
                    background-color: $theme-warning-primary;
                }
            }
        }
    }
}

    /*---Calender---*/
.theme-warning{
    .fx-element-overlay{
        .fx-card-item {
            .fx-card-content a:hover {
                color: $theme-warning-primary;
            }
            .fx-overlay-1 .fx-info > li a:hover {
                background: $theme-warning-primary;
                border-color: $theme-warning-primary;
            }
        }
    }
    .fc-event {
        background: $theme-warning-primary;
    }
    .calendar-event{
        @extend .fc-event
    }
}

    /*---Tabs---*/

.theme-warning {
    .tabs-vertical{
        li{
            .nav-link{
                @include hover-full-state{
                    background-color: $theme-warning-primary;
                    color: $white;    
                }
            }
        }
    }
    .customvtab{
        .tabs-vertical{
            li{
                .nav-link{
                    @include hover-full-state{
                        border-right: 2px solid $theme-warning-primary;
                        color: $theme-warning-primary;    
                    }
                }
            }
        }
    }
    .customtab2{
        li{
            a{
                &.nav-link{
                    @include hover-active-state{
                        background-color: $theme-warning-primary;    
                    }
                }
            }
        }
    }
}

    /*---Notification---*/
.theme-warning {
    .jq-icon-primary { 
        background-color: $theme-warning-primary; 
        color: $white; 
        border-color: $theme-warning-primary; 
    }
    .jq-icon-info { 
        background-color: $theme-warning-info; 
        color: $white; 
        border-color: $theme-warning-info; 
    }
    .jq-icon-success { 
        background-color: $theme-warning-success; 
        color: $white; 
        border-color: $theme-warning-primary; 
    }
    .jq-icon-error { 
        background-color: $theme-warning-danger; 
        color: $white; 
        border-color: $theme-warning-danger; 
    }
    .jq-icon-danger { 
        background-color: $theme-warning-danger; 
        color: $white; 
        border-color: $theme-warning-danger; 
    }
    .jq-icon-warning { 
        background-color: $theme-warning-warning; 
        color: $white; 
        border-color: $theme-warning-warning; 
    }
}

    /*---avatar---*/
.theme-warning {
    .avatar{
        &.status-primary::after {
            background-color: $theme-warning-primary;
        }
        &.status-info::after {
            background-color: $theme-warning-info;
        }
        &.status-success::after {
            background-color: $theme-warning-success;
        }
        &.status-danger::after {
            background-color: $theme-warning-danger;
        }
        &.status-warning::after {
            background-color: $theme-warning-warning;
        }
        &[class*='status-']::after {
            background-color: $theme-warning-primary;
        }
    }
    .avatar-add:hover {
        background-color: darken($theme-warning-primary, 10%);
        border-color: darken($theme-warning-primary, 10%);
    }
}

    /*---media---*/
.theme-warning {
    .media-chat{
        &.media-chat-reverse {
            .media-body {
                p {
                  background-color: $theme-warning-primary; 
                }
            }
        }
    }
    .media-right-out {
        a:hover {
            color: darken($theme-warning-primary, 10%);
        }
    }
}

    /*---control---*/
.theme-warning{
    .control{
        input{
        &:checked{
            &:focus~.control_indicator{
               background-color: $theme-warning-primary;  
            }  
            ~.control_indicator{
               background-color: $theme-warning-primary; 
            }
        }
        }  
        &:hover input:not([disabled]):checked~.control_indicator{
            background-color: $theme-warning-primary; 
        }
    }
}

    /*---flex---*/
.theme-warning{
    .flex-column{
        >li{
            >a{
                &.nav-link{
                    &.active{
                        border-left-color: $theme-warning-primary;
                        &:hover{
                            border-left-color: $theme-warning-primary;
                        }
                    }
                }
            }
        }
    }
}

    /*---pagination---*/
.theme-warning{
    .pagination{
        li{
            a{
                &.current{
                    border: 1px solid $theme-warning-primary;
                    background-color: $theme-warning-primary;
                    &:hover{
                        border: 1px solid $theme-warning-primary;
                        background-color: $theme-warning-primary;
                    }
                }
                &:hover{
                    border: 1px solid darken($theme-warning-primary, 10%);
                    background-color: darken($theme-warning-primary, 10%)!important;
                }
            }
        }
    }
    .dataTables_wrapper{
        .dataTables_paginate{
            .paginate_button.current{
                border: 1px solid $theme-warning-primary;
                background-color: $theme-warning-primary;
                    &:hover{
                        border: 1px solid $theme-warning-primary;
                        background-color: $theme-warning-primary;
                    }                
            } 
        }
    }
    .paging_simple_numbers{
        .pagination{
            .paginate_button{
                &.active a{
                    background-color: $theme-warning-primary;
                }
                &:hover a{
                    background-color: $theme-warning-primary;
                }
            }
        }
    }
    .footable{
        .pagination{
            li{
                a{
                    @include hover-active-state{
                        background-color: $theme-warning-primary;    
                    }
                }
            }
        }
    }
}
/*---dataTables---*/
.theme-warning {
    .dt-buttons {
        .dt-button {
            background-color: $theme-warning-primary;
        }
    }
}

/*---select2---*/
.theme-warning {
    .select2-container--default{
    &.select2-container--open {
        border-color: $theme-warning-primary;
    }
        .select2-results__option--highlighted[aria-selected] {
            background-color: $theme-warning-primary;
        }
        .select2-search--dropdown {
            .select2-search__field{
                border-color: $theme-warning-primary !important;
            }        
        }
        &.select2-container--focus{
            .select2-selection--multiple{
                border-color: $theme-warning-primary !important;
            }
        }
        .select2-selection--multiple:focus{
            border-color: $theme-warning-primary !important;
        } 
        .select2-selection--multiple {
            .select2-selection__choice{
                background-color: $theme-warning-primary;
                border-color: $theme-warning-primary;
            }
        }
    }
}

/*---Other---*/

.theme-warning{
    .myadmin-dd{
        .dd-list{
            .dd-list{
                .dd-handle:hover{
                    color: darken($theme-warning-primary, 10%);
                }
            }
        }
    }
    .myadmin-dd-empty{
        .dd-list{
            .dd3-handle:hover{
                color: darken($theme-warning-primary, 10%);
            }
            .dd3-content:hover{
                color: darken($theme-warning-primary, 10%);
            }
        }        
    }
    [data-overlay-primary]::before{
        background: darken($theme-warning-primary, 10%);
    }
}


/*---wizard---*/

.theme-warning{
    .wizard-content{
        .wizard{
            >.steps{
                >ul{
                    >li{
                    &.current{
                        border: 2px solid $theme-warning-primary;
                        background-color: $theme-warning-primary;
                    } 
                     &.done{
                        border-color: darken($theme-warning-primary, 10%);
                        background-color: darken($theme-warning-primary, 10%);
                    } 
                    }
                }
            }
            >.actions{
                >ul{
                    >li{
                        >a{
                            background-color: $theme-warning-primary;
                        }
                    }
                }
            }
        &.wizard-circle{
            >.steps{
                >ul{
                    >li{
                       &:after{
                            background-color: $theme-warning-primary;
                        }
                       &:before{
                            background-color: $theme-warning-primary;
                        }
                    }
                }
            }
        } 
        &.wizard-notification{
            >.steps{
                >ul{
                    >li{
                       &:after{
                            background-color: $theme-warning-primary;
                        }
                       &:before{
                            background-color: $theme-warning-primary;
                        }
                        &.current{
                            .step{
                                border: 2px solid $theme-warning-primary;
                                color: $theme-warning-primary;
                                &:after{
                                    border-top-color: $theme-warning-primary;
                                }
                            }
                        }
                        &.done{
                            .step{
                                &:after{
                                    border-top-color: $theme-warning-primary;
                                }
                            }
                        }
                    }
                }
            }
        } 
        }
    }
}
// Small devices
@include screen-sm-max {
    .theme-warning{
        .wizard-content{
            .wizard{
                >.steps{
                    >ul{
                        >li{
                            &:last-child{
                                &:after{
                                    background-color: $theme-warning-primary;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
// Small devices
@include screen-xs {
    .theme-warning{
        .wizard-content{
            .wizard{
                >.steps{
                    >ul{
                        >li{
                            &.current{
                                &:after{
                                    background-color: $theme-warning-primary; 
                                } 
                            }
                        }
                    }
                }
            }
        }
    }
}


 /*---slider---*/
.theme-warning{
    #primary {
        .slider-selection{
            background-color: $theme-warning-primary;
        }
    }
    #info {
        .slider-selection{
            background-color: $theme-warning-info;
        }
    }
    #success {
        .slider-selection{
            background-color: $theme-warning-success;
        }
    }
    #danger {
        .slider-selection{
            background-color: $theme-warning-danger;
        }
    }
    #warning {
        .slider-selection{
            background-color: $theme-warning-warning;
        }
    }
}

/*---horizontal-timeline---*/

.theme-warning{
    .cd-horizontal-timeline{
        .events{
            a{
                &.selected{
                    &::after{
                        background: $theme-warning-primary;
	                    border-color: $theme-warning-primary;
                    }
                }
                &.older-event::after{
                    border-color: $theme-warning-primary;
                }
            }
        }
        .filling-line{
            background: $theme-warning-primary;
        }
        a{
            color: $theme-warning-primary; 
            @include hover-focus-state{
                color: $theme-warning-primary;    
            }
        }
    }
    .cd-timeline-navigation{
        a{
            @include hover-focus-state{
                border-color: $theme-warning-primary;    
            }
        }
    }
}
