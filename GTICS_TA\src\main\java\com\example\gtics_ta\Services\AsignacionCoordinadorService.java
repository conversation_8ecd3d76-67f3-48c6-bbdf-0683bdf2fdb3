package com.example.gtics_ta.Services;

import com.example.gtics_ta.DTO.CalendarioSemanalDTO;
import com.example.gtics_ta.Entity.*;
import com.example.gtics_ta.Repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Time;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AsignacionCoordinadorService {
    
    @Autowired
    private HorariosCoordinadorRepository horariosCoordinadorRepository;
    
    @Autowired
    private ReservasRepository reservasRepository;
    
    @Autowired
    private UsuarioRepository usuarioRepository;
    
    @Autowired
    private EspaciosDeportivosRepository espaciosDeportivosRepository;
    
    /**
     * Obtiene todos los coordinadores disponibles
     */
    public List<Usuario> obtenerCoordinadores() {
        return usuarioRepository.findByRol_IdRol(3); // Rol coordinador = 3
    }
    
    /**
     * Obtiene el calendario semanal con asignaciones y reservas
     */
    public CalendarioSemanalDTO obtenerCalendarioSemanal(LocalDate fechaReferencia) {
        // Calcular inicio y fin de semana (lunes a domingo)
        LocalDate inicioSemana = fechaReferencia.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate finSemana = inicioSemana.plusDays(6);
        
        CalendarioSemanalDTO calendario = new CalendarioSemanalDTO();
        calendario.setFechaInicioSemana(inicioSemana);
        calendario.setFechaFinSemana(finSemana);
        
        // Crear días de la semana
        List<CalendarioSemanalDTO.DiaCalendario> dias = new ArrayList<>();
        String[] nombresDias = {"Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo"};
        
        for (int i = 0; i < 7; i++) {
            LocalDate fecha = inicioSemana.plusDays(i);
            CalendarioSemanalDTO.DiaCalendario dia = new CalendarioSemanalDTO.DiaCalendario(fecha, nombresDias[i]);
            
            // Obtener asignaciones del día
            dia.setAsignaciones(obtenerAsignacionesDia(fecha));
            
            // Obtener reservas del día
            dia.setReservas(obtenerReservasDia(fecha));
            
            dias.add(dia);
        }
        
        calendario.setDias(dias);
        
        // Obtener información de coordinadores
        calendario.setCoordinadores(obtenerInfoCoordinadores(inicioSemana, finSemana));
        
        return calendario;
    }
    
    /**
     * Obtiene las asignaciones de coordinadores para un día específico
     */
    private List<CalendarioSemanalDTO.AsignacionDia> obtenerAsignacionesDia(LocalDate fecha) {
        Date fechaDate = java.sql.Date.valueOf(fecha);

        // Obtener todos los horarios que incluyan esta fecha
        List<HorariosCoordinador> horarios = horariosCoordinadorRepository.findByFechaInicioBetween(
            fechaDate, fechaDate
        );

        // Filtrar horarios que realmente incluyan esta fecha
        List<HorariosCoordinador> horariosDelDia = horarios.stream()
            .filter(h -> {
                LocalDate fechaInicio = h.getFechaInicio().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                LocalDate fechaFin = h.getFechaFin().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                return !fecha.isBefore(fechaInicio) && !fecha.isAfter(fechaFin);
            })
            .collect(Collectors.toList());

        return horariosDelDia.stream().map(h -> {
            LocalTime horaInicio = h.getHoraEntrada().toLocalTime();
            LocalTime horaFin = h.getHoraSalida().toLocalTime();

            return new CalendarioSemanalDTO.AsignacionDia(
                h.getUsuario().getId(),
                h.getUsuario().getNombres() + " " + h.getUsuario().getApellidos(),
                h.getEspacio().getId(),
                h.getEspacio().getNombre(),
                h.getEspacio().getUbicacion(),
                horaInicio,
                horaFin
            );
        }).collect(Collectors.toList());
    }
    
    /**
     * Obtiene las reservas para un día específico
     */
    private List<CalendarioSemanalDTO.ReservaDia> obtenerReservasDia(LocalDate fecha) {
        List<Reservas> reservas = reservasRepository.findByFechaReserva(fecha);
        
        return reservas.stream().map(r -> {
            LocalTime horaInicio = r.getHorario().getHoraInicio().toLocalTime();
            LocalTime horaFin = r.getHorario().getHoraFin().toLocalTime();
            
            return new CalendarioSemanalDTO.ReservaDia(
                r.getId(),
                r.getUsuario().getNombres() + " " + r.getUsuario().getApellidos(),
                r.getEspacioDeportivo().getNombre(),
                horaInicio,
                horaFin,
                r.getEstadoReserva().toString()
            );
        }).collect(Collectors.toList());
    }
    
    /**
     * Obtiene información resumida de coordinadores en la semana
     */
    private Map<Integer, CalendarioSemanalDTO.CoordinadorInfo> obtenerInfoCoordinadores(LocalDate inicioSemana, LocalDate finSemana) {
        Date fechaInicio = java.sql.Date.valueOf(inicioSemana);
        Date fechaFin = java.sql.Date.valueOf(finSemana);
        
        List<Usuario> coordinadores = horariosCoordinadorRepository.findCoordinadoresEnSemana(fechaInicio, fechaFin);
        
        Map<Integer, CalendarioSemanalDTO.CoordinadorInfo> infoMap = new HashMap<>();
        
        for (Usuario coordinador : coordinadores) {
            CalendarioSemanalDTO.CoordinadorInfo info = new CalendarioSemanalDTO.CoordinadorInfo(
                coordinador.getId(),
                coordinador.getNombres(),
                coordinador.getApellidos(),
                coordinador.getCorreo()
            );
            
            // Calcular estadísticas
            List<HorariosCoordinador> horariosCoordinador = horariosCoordinadorRepository
                .findByUsuarioAndFechaInicioBetween(coordinador, fechaInicio, fechaFin);
            
            info.setTotalHorasAsignadas(calcularTotalHoras(horariosCoordinador));
            info.setTotalEspaciosAsignados(calcularEspaciosUnicos(horariosCoordinador));
            
            infoMap.put(coordinador.getId(), info);
        }
        
        return infoMap;
    }
    
    /**
     * Calcula el total de horas asignadas
     */
    private int calcularTotalHoras(List<HorariosCoordinador> horarios) {
        return horarios.stream()
            .mapToInt(h -> {
                long diff = h.getHoraSalida().getTime() - h.getHoraEntrada().getTime();
                return (int) (diff / (1000 * 60 * 60)); // Convertir a horas
            })
            .sum();
    }
    
    /**
     * Calcula el número de espacios únicos asignados
     */
    private int calcularEspaciosUnicos(List<HorariosCoordinador> horarios) {
        return (int) horarios.stream()
            .map(h -> h.getEspacio().getId())
            .distinct()
            .count();
    }
    
    /**
     * Asigna un coordinador a un horario específico
     */
    public boolean asignarCoordinador(Integer coordinadorId, Integer espacioId, 
                                    LocalDate fechaInicio, LocalDate fechaFin,
                                    LocalTime horaEntrada, LocalTime horaSalida) {
        
        Usuario coordinador = usuarioRepository.findById(coordinadorId).orElse(null);
        EspaciosDeportivos espacio = espaciosDeportivosRepository.findById(espacioId).orElse(null);
        
        if (coordinador == null || espacio == null) {
            return false;
        }
        
        // Verificar conflictos de horario
        Date fechaInicioDate = java.sql.Date.valueOf(fechaInicio);
        Date fechaFinDate = java.sql.Date.valueOf(fechaFin);
        
        List<HorariosCoordinador> conflictos = horariosCoordinadorRepository
            .findConflictosHorario(coordinador, fechaInicioDate, fechaFinDate);
        
        if (!conflictos.isEmpty()) {
            return false; // Hay conflictos
        }
        
        // Crear nueva asignación
        HorariosCoordinador nuevoHorario = new HorariosCoordinador();
        nuevoHorario.setUsuario(coordinador);
        nuevoHorario.setEspacio(espacio);
        nuevoHorario.setFechaInicio(fechaInicioDate);
        nuevoHorario.setFechaFin(fechaFinDate);
        nuevoHorario.setHoraEntrada(Time.valueOf(horaEntrada));
        nuevoHorario.setHoraSalida(Time.valueOf(horaSalida));
        
        horariosCoordinadorRepository.save(nuevoHorario);
        return true;
    }
    
    /**
     * Obtiene las reservas sin coordinador asignado
     */
    public List<Reservas> obtenerReservasSinCoordinador() {
        return reservasRepository.findReservasSinCoordinador();
    }
}
