nav {
    border-bottom: solid 1px $light;
    min-height: 60px;
    background: $white;
	font-size: $fs-14;
	font-family: $bodyfont;
	font-weight: $fw-400;
    .wrap-core-nav-list, .nav-header{
        background: $white;
    }
    .brand {
        display: block;
        position: relative;
        width: 170px;
        padding: 20px 0px;
        img {
            width: 100%;
        }
    }
    .menu {
        li {
                
            a {
                display: block;
                padding: 20px 0px;
                margin-left: 15px;
                margin-right: 15px;
                text-decoration: none;
                color: $dark;
                text-transform: uppercase; 
                border-bottom: 1px solid transparent;
                &:hover{
                    color: $primary;
                }
            }
            &.active{
                >a{
                  color: $success !important; 
                  border-bottom: 1px solid $success; 
                }
            }
        }
        .megamenu-content{
            li{
                a{
                    text-transform: capitalize;
                    font-size: $fs-14;
                    font-family: $bodyfont;
                    font-weight: $fw-400;
                }
            }
        }
    }
    &.nav-white{
        &.nav-transparent{            
            .menu {
                li {
                    a {
                        color: $white;
                        &:hover{
                            color: $success;
                        }   
                    }
                    &.active{
                        > a{
                            color: $success !important;
                        }
                    }
                }
            }
            ul{
                &.attributes{
                    li{
                        a{
                           color: $white;
                            &:hover{
                                color: $success;
                            }  
                        }
                    }
                }
            }
            &.nav-core-sticky{
                &.on-scroll{
                    .menu{
                        li{
                            a{
                                color: $dark;
                                &:hover{
                                    color: $success;
                                }
                            }
                        }
                    }
                    ul{
                        &.attributes{
                            li{
                                a{
                                    color: $dark;
                                    &:hover{
                                        color: $success;
                                    }
                                }
                            }
                        }
                    }
                    &.core-nav{
                        .dropdown >{
                            .dropdown-menu >{
                                li{
                                    a{
                                        color: $dark;
                                        &:hover{
                                            color: $success;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    .menu{
                        .megamenu-content{
                            li{
                                a{
                                    color: $dark;
                                    &:hover{
                                        color: $success;
                                    }
                                }
                            }
                        }
                    }
                }
                &.core-nav{                    
                    .dropdown >{
                        .dropdown-menu{
                            li{
                                a{
                                   color: $dark;
                                    &:hover{
                                        color: $success;
                                    } 
                                }
                            }
                        }
                    }
                }
                .menu{
                    .megamenu-content{
                        li{
                            a{
                                color: $dark;
                                &:hover{
                                    color: $success;
                                }
                            }
                        }
                    }
                }
            }
        } 
    }  
    button {
        background: transparent;
        border: none;
        outline: none;
    }
}
.core-nav{
    &.nav-core-sticky{
        &.on-scroll{
            z-index: 9999999999;
        }
    }
    .dropdown >{
        .dropdown-menu >{
            li >{
                a{                    
                    text-transform: capitalize;
                    font-size: $fs-14;
                    font-family: $bodyfont;
                    font-weight: $fw-400;
                    margin-left: 0;
                    margin-right: 0;
                }
            }
        }
    }
}
.left-menu{
    .core-nav{
        .wrap-core-nav-list {
            text-align: left;
        }
        .nav-header {
            float: left;    
            position: relative;
            display: inline-block;
        }
    }
}


@media (max-width: 992px){
    .left-menu{
        .core-nav{
            .nav-header {
                float: none;
                display: block;
                position: relative;
            }
        }
    }
}

/*
DROPDOWN STYLE
=========================== */
nav{
    .menu {
        >li{
            &.dropdown{
                >a::before{
                    margin-left: 5px;
                    content: '\f107';
                    font-family: "FontAwesome";
                    float: right;
                    position: relative;
                }
                li{
                    &.dropdown {
                         > a::before{
                            margin-left: 5px;
                            content: '\f107';
                            font-family: "FontAwesome";
                            float: right;
                            position: relative;
                        }
                    }
                }
            }
            &.dropdown{                
                &>.open>{
                    a{
                        color: $success;
                    }
                }
            }
            &.megamenu{                
                >a::before{
                    margin-left: 5px;
                    content: '\f107';
                    font-family: "FontAwesome";
                    float: right;
                    position: relative;
                }
            }
        }
    }
    &.nav-white{
        &.nav-transparent{
            .menu{
                >li{
                    &.dropdown{
                        >a::before{
                            color: $white;
                        }
                    }
                    &.megamenu{                        
                        >a::before{
                            color: $white;
                        }
                    }
                }
            }
            &.nav-core-sticky{
                &.on-scroll{
                    .menu{
                        >li{
                            &.dropdown {
                                >a::before{
                                    color: $dark;
                                }
                            }
                            &.megamenu {
                                >a::before{
                                    color: $dark;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
.sample-text{
    padding: 30px;
}
.core-nav .wrap-core-nav-list.right .dropdown>.dropdown-menu .dropdown>.dropdown-menu {
    left: auto;
}


/* =========================== 
Attributes Menu
=========================== */
nav{
    ul{
        .attributes{
            li{
                a{
                    .feather{
                        width: 16px;
                        height: 16px;
                    }
                }
            }
        }
    }
}

/*
MEGAMENU SHOPPING CART
=========================== */
.megamenu-cart{
    box-shadow: 0 0 20px 0 rgba(62,28,131,0.1);
    margin-top: -1px;
}

nav {
    .cart-header{
        border-bottom: solid 1px $light;
        padding: 15px 20px;
        .feather{
            width: 16px;
            height: 16px;
        }
        .badge {
            font-size: 8px;
            line-height: 4px;
            display: inline-block;
            background: $success;
            color: $white;
            border-radius: $default-border-radius;
            padding: 4px;
            position: relative;
            top: -6px;
            margin-left: 3px;
        }
        .total-price{
            float: right;
            span{
                color: $success;
            }
        }
    }
    .cart-body {
        ul{
            padding: 0;
            margin: 0;
            list-style: none;
            display: block;
            li{
                position: relative;
                display: block;
                float: none;
                width: 100%;
                padding: 15px 20px;
                border-top: solid 1px $light;
                min-height: 90px;
                padding-left: 90px;
                &:first-child{
                    border: 0;
                }
                img{
                    width: 50px;
                    top: 20px;
                    left: 20px;
                    position: absolute;
                    border-radius: 4px;
                }
                .title{
                    text-transform: none;
                    font-weight: 700;
                    margin: 5px 0;
                }
                .price-st{
                    color: $success;
                }
                .qty{
                    float: right;
                }
                .link{
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    &:hover{
                        background: rgba($white, 0.3)
                    }
                }
            }
        }
    }
    .cart-footer{
        border-top: solid 1px $light;
        padding: 15px 20px;
        > a{
            padding: 15px !important;
            display: block;
            width: 100%;
            border-radius: $default-border-radius;
            background: $success;
            color: $white !important;
            box-shadow: 0 0 20px 0 rgba(62,28,131,0.1);
            text-align: center;
            text-decoration: none;
            &:hover{
                opacity: 0.8;
            }
        }
    }
}

/*
Megamenu STYLE
=========================== */
nav{
    .menu{
        .megamenu-content {
            padding: 25px;
            div{
                >.list-group{
                    border-right: 1px dashed $gray-400;
                }
                &:last-child{                    
                    >.list-group{
                        border-right: 0;
                    }
                }
            }
            
            li {
                a {
                    padding: 10px 15px;
                    margin-left: 0;
                    margin-right: 0;    
                    border-bottom: 0;
                }
                .menu-title{
                    padding: 10px 15px;
                    margin-bottom: 0;
                }
            }
        }
    }
}
.core-nav .megamenu>.megamenu-content{
    max-width: 1170px;
}
@media (max-width: 992px) {
    nav{
        .menu{
            .megamenu-content {
                padding: 5px;                
                div{
                    >.list-group{
                        border-right: 0;
                    }
                }
                li:last-child{
                    a{
                        border-bottom: solid 1px $light;
                    }
                }
            }
        }
    }
	.core-nav .wrap-core-nav-list {
		overflow-y: auto;
		overflow-x: hidden;
	}
}

/* =========================== */
nav{
    &.core-nav{
        &.nav-core-fixed{
            background-color: transparent;
	        border-bottom:none;
        }
        &.nav-core-sticky{
            background-color: transparent;
	        border-bottom:none;
            &.on-scroll{
                 background: $white;
                .wrap-core-nav-list, .nav-header{                    
                    background: $white;
                }
            }
        }
    } 
    &.nav-core-fixed{
        .wrap-core-nav-list, .nav-header{
            background: transparent;
        }
    }
    &.nav-core-sticky{
        .wrap-core-nav-list, .nav-header{
            background: transparent;
        }
    }
}
.top-bar{
    .core-nav{
        &.nav-core-sticky {
            top: 50px;
            &.on-scroll {
                top: auto;
            }
        }
    }
    &.dualbar{
        .core-nav{
            &.nav-core-sticky {
                top: 102px;
                &.on-scroll {
                    top: auto;
                }
            }
        }
    }
}
.dualbar{
    .core-nav{
        &.nav-core-sticky {   
            padding: 0px 0px;
        }  
    }
    nav {
        min-height: 45px;
        .menu {
            li {
                a {
                    padding: 10px 10px;
                }
            }
        }        
        ul.attributes li a {
            padding: 12px 20px;
        }
    }
    .topbar {
        border-bottom: none;
        padding-top: 0;
        >.top-bg{
            background-color: rgba($dark, 0.2);
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }
}
.core-nav{
    &.nav-core-sticky {
        position: absolute;
        top: auto;
        left: auto;
        width: 100%;
        z-index: 99999999;    
        padding: 0px 0px;
    }  
}
.middlebar{    
    padding: 15px 0 0;
    position: relative;
    width: 100%;    
	min-height: 50px;
}
.topbar {
    .btn{        
        background: transparent;
        padding: 0 10px;
        border-radius: 0;
        border: none !important;
        @include hover-full-state{
            border: none !important;
            box-shadow: none !important;
        }
    }
    .btn-group{        
        @include hover-full-state{
            border: none !important;
            box-shadow: none !important;
        }        
        .dropdown-menu{            
            a{
                color: $dark;
                margin-left: 0;
            }
        }
    }
    border-bottom: 1px solid rgba($white, 0.1);
    padding: 14px 0;
	z-index: 999999999;
    position: absolute;
    width: 100%;    
	min-height: 50px;
    ul {
        li {
            display: inline-block;
            color: $white;
            font-size: $fs-12;
            font-weight: 300;
            position: relative;
            .btn{                
                font-weight: 300;
            }
        }
    }    
    .topbar-left ul li:after,
    .topbar-right ul li:after{
        position: absolute;
        width: 1px;
        height: 20px;
        background-color: $white;
        right: 0;
        content: "";
        top: 2px;
        opacity: 0.2;
    }
    .topbar-right ul li:after{
        right:auto;
        left:0;
    }
    .topbar-right ul li:first-child:after,
    .topbar-left ul li:last-child:after{
        content:none;
    }
}
.text-dark{
   .topbar {
        border-bottom: 1px solid rgba($dark, 0.1);
        ul {
            li {
                color: $dark;
            }
        }    
        .topbar-left ul li:after,
        .topbar-right ul li:after{
            background-color: $dark;
        }
    } 
    .topbar-social{    
        ul{
            li{
                a{
                    color: $dark; 
                    &:hover {
                        color: $success;
                    }
                }
            }
        }
    }
    .topbar-call {
        ul {
            li {
                a {                
                    color: $dark;
                }
                i {
                    color: $success;
                }
            }
        }
    }
}

.topbar-call {
    transition: all 0.5s ease-in-out;
    -webkit-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    ul {
        li {
            a {                
                color: $white;
            }
            i {
                margin-right: 5px;
                color: $success;
                &.flag-icon{
                    width: 1em;
                }
            }
        }
    }
}
.topbar-social{    
    position: relative;
    transition: all 0.5s ease-in-out;
    -webkit-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    ul{
        li{
            a{
                color: $white; 
                margin-left: 5px;
                &:hover {
                    color: $success;
                }
                span {
                    margin-right: 5px;
                    font-size: $fs-12;
                }
            }
        }
    }
}

.dark-overlay-top {
    .topbar {
        background: rgba($dark, 0.4);
    }
}

@media (max-width: 992px) {
    nav{
        .full-container, .nav-container{            
            padding-left: 0;
            padding-right: 0;
        }
        .nav-header {
            min-height: 60px;
            .brand {
                margin: 0 auto 0px;
                float: none;
                width: 130px;
                padding: 20px 0px;
            }
            .toggle-bar {
                font-size: $fs-18;
                position: absolute;
                top: 17px;
                left: 15px;
            }
        }
        .menu{
            li{
                a{
                    padding: 10px 15px;
                    border-bottom: solid 1px $light;
                }
                &:last-child{
                    a{
                       border-bottom: none; 
                    }
                }
            }
        }
        &.nav-white.nav-transparent{
            &.core-nav{
                .wrap-core-nav-list{
                    .core-nav-list {
                        background: $white;
                    }
                }
            }
            .menu{
                li{
                    a{
                        color: $dark;
                    }
                }
                >li{
                    &.dropdown, &.megamenu{
                        >a::before{
                            color: $dark;
                        }
                    }
                }
            }
        }
        
        &.nav-dark.nav-transparent{
            &.core-nav{
                .wrap-core-nav-list{
                    .core-nav-list {
                        background: $white;
                    }
                }
            }
            .menu{
                li{
                    a{
                        color: $dark;
                    }
                }
                >li{
                    &.dropdown, &.megamenu{
                        >a::before{
                            color: $dark;
                        }
                    }
                }
            }
        }
    }    
	.core-nav .wrap-core-nav-list .core-nav-list li {
		clear: both;
	}    
    .top-bar.dualbar{
        .topbar{
            padding-bottom:0;
        }
        .core-nav.nav-core-sticky {
            top: 43px;
        }
        nav .nav-header{
            min-height: auto;
        }
        nav .nav-header .toggle-bar {
            top: 12px;
        }
        nav .nav-header .brand {
            margin: 0 auto 0px;
            padding: 10px 0px;
        }
    }
}

@media (max-width: 767px) {
	.topbar {
		padding: 5px 0;
        ul li {
            font-size: $fs-12;
        }
        .topbar-right{
            margin-top: 4px;
        }
	}
	.top-bar .core-nav.nav-core-sticky {
		top: 57px;
	}
    .top-bar.dualbar {
        .core-nav.nav-core-sticky {
            top: 70px;
        }
    }
}
nav.full-width .nav-container{
	padding: 0 15px;
	width: 100%;
}
@media (max-width: 992px){
	nav.full-width .nav-container {
		padding-left: 0;
		padding-right: 0;
	}
}

nav{
    &.core-nav{
        &.nav-core-fixed, &.nav-core-sticky{
            &.dark-overlay{
               background-color: rgba($dark, 0.4);
	           border-bottom:none; 
            }
        }
    }
    &.nav-core-fixed{
        &.dark-overlay{
            .wrap-core-nav-list, .nav-header{
                background: rgba($dark, 0.4);
            }
        }
    }
    &.dark-overlay{
        &.core-nav{
            &.nav-core-sticky{
                &.on-scroll{
                    background: $dark;
                }
            }
        }
        &.nav-core-sticky{
            &.on-scroll{
                .wrap-core-nav-list, .nav-header {
                    background: $dark;
                }
            }
        }
    }
    &.nav-white{
        &.nav-transparent{
            &.dark-overlay{
                .menu{
                    li{
                        a{
                            color: $white;
                        }
                    }
                }                
                .menu {
                    .megamenu-content{
                        li{
                            a{
                                &:hover{
                                    color: $success;
                                }
                            }                    
                            &.active{
                                > a{
                                    color: $success !important;
                                }
                            }
                        }
                    }
                }  
                ul.attributes li a {
                    color: $white;
                }
                &.nav-core-sticky{                    
                    .menu li a, ul.attributes li a, &.core-nav .dropdown>.dropdown-menu>li>a, .menu .megamenu-content li a,{
                        color: $white;
                        &:hover{
                            color: $success;
                        }
                    }
                    &.on-scroll{
                        .menu li a, ul.attributes li a, &.core-nav .dropdown>.dropdown-menu>li>a, .menu .megamenu-content li a,{
                            color: $white;
                            &:hover{
                                color: $success;
                            }
                        }
                    }                    
                    .core-nav .dropdown>.dropdown-menu>li>a, .menu .megamenu-content li a{
                        color: $white;
                        &:hover{
                            color: $success;
                        }
                    }
                }
                .menu li a, ul.attributes li a{                    
                    &:hover{
                        color: $success !important;
                    }
                }
                .menu > li.dropdown > a, .menu > li.megamenu > a{
                    &::before{
                        color: $white;
                    }
                }
                &.nav-core-sticky.on-scroll .menu > li.dropdown > a, .nav-core-sticky.on-scroll .menu > li.megamenu > a{
                    &::before{
                        color: $white;
                    }
                }
                &.core-nav {
                    .megamenu>.megamenu-content{
                        background: $dark;
                        border: solid 1px $dark;
                        color: $white;
                    }
                    .dropdown>.dropdown-menu{
                        background: $dark;
                        border: none;
                    }
                    .dropdown>.dropdown-menu>li>a {
                        border-bottom: solid 1px $dark;
                    }
                    .cart-header {
                        border-bottom: solid 1px $dark;
                        .total-price, i{
                            color: $white;
                        }
                    }
                    .cart-body{
                        ul{
                            li{
                                .title {
                                    color: $light;
                                }
                                border-top: solid 1px lighten($dark, 10%);
                            }
                        }
                    }
                    .cart-footer{
                        border-top: solid 1px lighten($dark, 10%);
                    }
                }
            }
        }
    }
}

@media (max-width: 992px){
    nav{
        &.nav-white.nav-transparent.dark-overlay{
            &.core-nav{
                .wrap-core-nav-list{
                    .core-nav-list {
                        background: $dark;
                    }                    
                    border-bottom: solid 1px $dark;
                    border-top: solid 1px $dark;
                }
            }
            .menu li a{
                border-bottom: solid 1px $dark;
		        border-top: solid 1px $dark;
            }
        } 
    }    
}
.header-light {
    .core-nav{
        &.nav-core-sticky {
            position: relative;
            &.on-scroll {
                position: fixed;
            }
        }
    }
    nav{
        &.nav-core-fixed{
            .wrap-core-nav-list, .nav-header{
                background: $white;
            }
        }
        &.core-nav{
            .nav-core-sticky{                
                background: $white;
            }
        }
        &.nav-core-sticky{
            .wrap-core-nav-list, .nav-header {
                background: $white;
            }
        }
    }
    .topbar{
        ul{
            li{
               color: $dark; 
            }
        }
        border-bottom: 1px solid rgba($dark, 0.1);
	    background: $white;
    }
    .topbar-call ul li a, .topbar-social ul li a{
        color: $dark; 
    }
}

@media (max-width: 992px){
	.core-nav .nav-header {
		z-index: 9;
	}
}
.header-light{
    .core-nav.nav-core-sticky{
        &.on-scroll {
            padding: 0px 0px;
        }
    }
}
.header-dark{
    .core-nav.nav-core-sticky {
        position: relative;
        &.on-scroll {
            position: fixed;
        }
    }
    nav{
        &.nav-core-fixed{
            .wrap-core-nav-list, .nav-header{
                background: $dark;
            }
        }
        &.core-nav{
            &.nav-core-sticky{
                background: $dark;
            }
        }
        &.nav-core-sticky{
            .wrap-core-nav-list, .nav-header {
                background: $dark;
            }
        }
    }
    .topbar ul li, .topbar-call ul li a, .topbar-social ul li a, nav .menu li a, ul.attributes li a{
        color: $white;
    }
    nav .menu{
        li .dropdown-menu a, .megamenu-content li a{
            color: $dark;
        }
    }
    topbar {
        border-bottom: 1px solid rgba($white, 0.1);
        background: $dark;
    }
}

@media (max-width: 992px){
    .header-dark{
        nav .menu li a{
            border-bottom: solid 1px $dark;
        }
        .core-nav{
            .wrap-core-nav-list{
                border-bottom: solid 1px $dark;
                border-top: solid 1px $dark;
            }
            .dropdown>.dropdown-menu, .megamenu>.megamenu-content{
                background: $dark;
            }
        }
        nav .menu{
            li .dropdown-menu a, .megamenu-content li a {
                color: $white;
            }
        }
        .core-nav .dropdown>.dropdown-menu>li>a, nav .menu .megamenu-content li:last-child a, .core-nav .megamenu>.megamenu-content{
            border-bottom: solid 1px $dark;
        }
    }
}
.header-dark .core-nav.nav-core-sticky.on-scroll {
    padding: 0px 0px;
}
.header-dark-topbar{
    .core-nav.nav-core-sticky {
        position: relative;
        &.on-scroll {
            position: fixed;
        }
    }
    nav{
        &.nav-core-fixed{
            .wrap-core-nav-list, .nav-header{
                background: $white;
            }
        }
        &.core-nav{
            &.nav-core-sticky{
                background: $white;
            }
        }
        &.nav-core-sticky{
            .wrap-core-nav-list, .nav-header{
                background: $white;
            }
        }
    }
    .topbar ul li, .topbar-call ul li a, .topbar-social ul li a{
        color: $white;
    }
    nav{
        .menu li a{
            color: $dark;
        }
        .menu li .dropdown-menu a, .menu .megamenu-content li a{
            color: $dark;
        }
    }
    ul{
        &.attributes li a{
            color: $dark;
        }
    }
    .topbar{
        border-bottom: 1px solid rgba($white, 0.1);
        background: $dark;
    }
    .core-nav.nav-core-sticky.on-scroll {
        padding: 0px 0px;
    }
}
header{
    &.header-fancy-topbar{
        position: relative;
        height: 90px;
        background-color: $success;
    }
    nav.header-fancy{
        .nav-container{
            background-color: $white;    
        }
        &.core-nav{
            padding:0px;
            top: 30px;
        }
        .menu li a {
            padding: 20px 0px;
        }
        .brand {
            padding: 20px 0px;
        }
        ul.attributes li a {
            padding: 20px 10px;
        }
        .menu .megamenu-content li a {
            padding: 10px 15px;
        }
        .menu {
            .megamenu-content{
                li{
                    a{
                        color: $dark;
                        &:hover{
                            color: $success;
                        }
                    }                    
                    &.active{
                        > a{
                            color: $success !important;
                        }
                    }
                }
            }
        }                    
    }
}

@media (max-width: 992px){
    header{
        nav.header-fancy{
            &.nav-core-fixed{
                .wrap-core-nav-list, .nav-header{
                    background: $white;
                    border-bottom: none;
                }
            } 
            &.nav-core-sticky{
                .wrap-core-nav-list, .nav-header{
                    background: $white;
                    border-bottom: none;
                }
            }
            &.core-nav{
                top: 9px;
            }
            .menu li a {
                padding: 10px 20px;
            }
        }
    }
    .core-nav .wrap-core-nav-list .core-nav-list li a {
        margin-left: 0;
        margin-right: 0;
    }
}
@media (max-width: 767px){
	header nav.header-fancy.core-nav {
		top: 0px;
	}
}
.core-content {
    .wrap-search-fullscreen {
        .nav-container {
            padding-top: 0;
            padding-left: 0;
            padding-right: 0;
            height: 100%;
            >.container, >.container-fluid {
                top: 40%;
                position: absolute;
            }
        }
        .close-search {
            right: 40px;
            top: 0;
            padding: 15px 0;
        }
    }
}

.header-fancy-topbar {
    .topbar {
        border-bottom: 0px solid rgba($white, 0.1);
        padding: 14px 0 14px;
    }
    .topbar-call ul li i {
        color: $white;
    }
    .topbar-social ul li a:hover {
        color: $dark;
    }
}
header nav.header-fancy.on-scroll .nav-container {
    box-shadow: none;
}

@media (max-width: 767px){
	.header-fancy-topbar .topbar {
		padding: 5px 0;
	}
	header.top-bar nav.header-fancy.core-nav.nav-core-sticky {
		top: 57px;
        &.on-scroll {
            top: 0;
        }
	}
}

.header-full-width nav.full-width .nav-container {
    padding: 0 15px;
    width: 100%;
}
nav{
    &.side-nav{
        .wrap-core-nav-list {
            background: $white;
        }
        .nav-header {
            background: $white;
            padding: 30px 25px;
            margin-bottom: 30px;
            .brand {
                display: block;
                position: relative;
                width: 170px;
                img {
                    width: 100%;
                }
            }
        }
        .menu li a {
            display: block;
            padding: 10px 25px;
            text-decoration: none;
            color: $dark;
            &:hover {
                color: $success;
            }
        }
    } 
}

/*
DROPDOWN STYLE
=========================== */
nav{
    &.side-nav{
        .menu{
            li{
                &.dropdown > a::before{
                    margin-left: 10px;
                    content: '';
                    border-top: solid 5px $dark;
                    border-left: solid 5px transparent;
                    border-right: solid 5px transparent;
                    float: right;
                    position: relative;
                    top: 8px;
                }
                &.dropdown.open > a{
                    color: $success;
                }
            }
        }
    }
}

/* =========================== */

@media (max-width: 992px) {
    nav{
        &.side-nav{
            nav-header {
                padding: 0;
                min-height: 60px;
                margin-bottom: 0;
                border-bottom: solid 1px $light;
                .brand {
                    margin: 0 auto 5px;
                    float: none;
                    display: block;
                    width: 170px;
                    position: relative;
                    top: 17px;
                    img {
                        width: 100%;
                    }
                }
                .toggle-bar {
                    font-size: $fs-18;
                    position: absolute;
                    top: 17px;
                    left: 15px;
                }
            }
            .menu li{
                a{
                    padding: 10px 10px;
                    border-bottom: solid 1px $light;
                }
                &:last-child a {
                    border-bottom: 0;
                }
            }
        }
    }    
}
.text-dark.top-bar{
    .lng-drop{
        .btn-group{  
            .btn{   
                color: $dark;
            }
        }
    }
}
.top-bar{
    ul{
        margin: 0;
        padding: 0;
        &.list-inline > li{
            padding-left: 0;
            padding-right: 0;
        }
    }
    .lng-drop{
        .btn-group{  
            z-index: 999;
            padding: 0;
            .btn{   
                font-size: $fs-12;
                background: transparent !important;
                border: none;
                color: #ffffff;
                padding: 0;
                @include hover-state{
                    box-shadow: none;
                    border: none;
                    outline: none !important ;
                    outline-offset: 0;
                }
                &.dropdown-toggle::after {                    
                    margin-left: -3px;
                    top: 2px;
                    position: relative;
                }
            }
            .dropdown-menu{
                li {
                    display: block;
                    a{
                        margin-right: 0;
                        margin-left: 0;
                    }
                }
            }
        }
    }
}
header{
    a, button, input {
        outline: medium none !important;
        color: $success;
    }
}
/* Fixed/sticky icon bar (vertically aligned 50% from the top of the screen) */
.icon-bar-sticky  {
    position: fixed;
    top: 35%;
    -webkit-transform: translateY(-35%);
    -ms-transform: translateY(-35%);
    transform: translateY(-35%);
    z-index: 9;
    right: 0;
    a {
        display: block;
        text-align: center;
        transition: all 0.3s ease;
        border-radius: 0;
        padding: 16px;
        margin-bottom: 5px;
    }
}
