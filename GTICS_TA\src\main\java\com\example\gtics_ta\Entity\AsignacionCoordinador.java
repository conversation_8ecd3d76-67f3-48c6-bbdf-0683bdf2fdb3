package com.example.gtics_ta.Entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;

@Entity
@Getter
@Setter
@Table(name = "asignacion_coordinador")
public class AsignacionCoordinador {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_asignacion")
    private Integer id;
    
    @ManyToOne
    @JoinColumn(name = "id_reserva", nullable = false)
    private Reservas reserva;
    
    @ManyToOne
    @JoinColumn(name = "id_coordinador", nullable = false)
    private Usuario coordinador;
    
    @ManyToOne
    @JoinColumn(name = "asignado_por", nullable = false)
    private Usuario asignadoPor;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Column(name = "fecha_asignacion", nullable = false)
    private LocalDate fechaAsignacion;
    
    @DateTimeFormat(pattern = "HH:mm")
    @Column(name = "hora_inicio", nullable = false)
    private LocalTime horaInicio;
    
    @DateTimeFormat(pattern = "HH:mm")
    @Column(name = "hora_fin", nullable = false)
    private LocalTime horaFin;
    
    @Column(name = "ubicacion", length = 200)
    private String ubicacion;
    
    @Column(name = "observaciones", columnDefinition = "TEXT")
    private String observaciones;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "estado_asignacion")
    private EstadoAsignacion estadoAsignacion = EstadoAsignacion.PENDIENTE;
    
    @Column(name = "fecha_creacion")
    private Timestamp fechaCreacion;
    
    @Column(name = "fecha_actualizacion")
    private Timestamp fechaActualizacion;
    
    // Constructor por defecto
    public AsignacionCoordinador() {
        this.fechaCreacion = new Timestamp(System.currentTimeMillis());
        this.fechaActualizacion = new Timestamp(System.currentTimeMillis());
    }
    
    // Enum para estado de asignación
    public enum EstadoAsignacion {
        PENDIENTE,      // Asignación creada pero no confirmada
        CONFIRMADA,     // Coordinador confirmó la asignación
        EN_PROGRESO,    // Coordinador está en el lugar
        COMPLETADA,     // Trabajo completado
        CANCELADA       // Asignación cancelada
    }
    
    // Método para actualizar timestamp
    @PreUpdate
    public void preUpdate() {
        this.fechaActualizacion = new Timestamp(System.currentTimeMillis());
    }
}
