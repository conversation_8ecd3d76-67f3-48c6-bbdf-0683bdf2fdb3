<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Admin - Gestión de Coordinadores</title>

    <!-- Vendors Style-->
    <link rel="stylesheet" th:href="@{/css/vendors_css.css}">
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/skin_color.css}">

    <!-- Custom CSS para calendario -->
    <style>
        .calendario-semanal {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .dia-calendario {
            border-right: 1px solid #e0e0e0;
            min-height: 400px;
            position: relative;
        }
        
        .dia-calendario:last-child {
            border-right: none;
        }
        
        .dia-header {
            background: #f8f9fa;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .dia-content {
            padding: 10px;
            height: 350px;
            overflow-y: auto;
        }
        
        .asignacion-item {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .reserva-item {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .reserva-sin-coordinador {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        
        .coordinador-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .selector-semana {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .btn-semana {
            margin: 0 5px;
        }
        
        .form-asignacion {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .estadisticas-coordinador {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .estadistica {
            text-align: center;
        }
        
        .estadistica .numero {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }
        
        .estadistica .label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">
        <div class="d-flex align-items-center logo-box justify-content-center">
            <a th:href="@{/admin}" class="logo">
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
                </div>
            </a>
        </div>
        <nav class="navbar navbar-static-top">
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>
                </ul>
            </div>
            <div th:replace="~{fragments :: navbarAdmin}"></div>
        </nav>
    </header>

    <aside th:replace="~{fragments :: sideBarAdmin}"></aside>

    <div class="content-wrapper">
        <div class="container-full">
            <div class="content-header">
                <div class="d-flex align-items-center">
                    <div class="me-auto">
                        <h4 class="page-title">Gestión de Coordinadores</h4>
                        <div class="d-inline-block align-items-center">
                            <nav>
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a th:href="@{/admin}"><i class="mdi mdi-home-outline"></i></a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Coordinadores</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <section class="content">
            <!-- Selector de Semana -->
            <div class="selector-semana">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fa fa-calendar me-2"></i>
                            Semana del <span th:text="${#temporals.format(calendario.fechaInicioSemana, 'dd/MM/yyyy')}"></span>
                            al <span th:text="${#temporals.format(calendario.fechaFinSemana, 'dd/MM/yyyy')}"></span>
                        </h5>
                    </div>
                    <div class="col-md-6 text-end">
                        <a th:href="@{/admin/coordinadores(semana=${calendario.fechaInicioSemana.minusWeeks(1)})}" 
                           class="btn btn-outline-primary btn-semana">
                            <i class="fa fa-chevron-left"></i> Semana Anterior
                        </a>
                        <a th:href="@{/admin/coordinadores}" class="btn btn-primary btn-semana">
                            <i class="fa fa-calendar-day"></i> Semana Actual
                        </a>
                        <a th:href="@{/admin/coordinadores(semana=${calendario.fechaInicioSemana.plusWeeks(1)})}" 
                           class="btn btn-outline-primary btn-semana">
                            Semana Siguiente <i class="fa fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Calendario Semanal -->
            <div class="calendario-semanal mb-4">
                <div class="row g-0">
                    <div th:each="dia : ${calendario.dias}" class="col dia-calendario">
                        <div class="dia-header">
                            <div th:text="${dia.nombreDia}"></div>
                            <small th:text="${#temporals.format(dia.fecha, 'dd/MM')}"></small>
                        </div>
                        <div class="dia-content">
                            <!-- Asignaciones de coordinadores -->
                            <div th:each="asignacion : ${dia.asignaciones}" class="asignacion-item">
                                <strong th:text="${asignacion.coordinadorNombre}"></strong><br>
                                <small th:text="${asignacion.espacioNombre}"></small><br>
                                <small>
                                    <i class="fa fa-clock"></i>
                                    <span th:text="${#temporals.format(asignacion.horaInicio, 'HH:mm')}"></span>
                                    -
                                    <span th:text="${#temporals.format(asignacion.horaFin, 'HH:mm')}"></span>
                                </small>
                            </div>
                            
                            <!-- Reservas -->
                            <div th:each="reserva : ${dia.reservas}" 
                                 th:class="${reserva.tieneCoordinador ? 'reserva-item' : 'reserva-item reserva-sin-coordinador'}">
                                <strong>Reserva: <span th:text="${reserva.usuarioNombre}"></span></strong><br>
                                <small th:text="${reserva.espacioNombre}"></small><br>
                                <small>
                                    <i class="fa fa-clock"></i>
                                    <span th:text="${#temporals.format(reserva.horaInicio, 'HH:mm')}"></span>
                                    -
                                    <span th:text="${#temporals.format(reserva.horaFin, 'HH:mm')}"></span>
                                </small>
                                <div th:if="${!reserva.tieneCoordinador}" class="mt-1">
                                    <span class="badge bg-danger">Sin coordinador</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Formulario de Asignación -->
                <div class="col-md-8">
                    <div class="form-asignacion">
                        <h5 class="mb-3">
                            <i class="fa fa-user-plus me-2"></i>
                            Asignar Coordinador
                        </h5>
                        
                        <form th:action="@{/admin/coordinadores/asignar}" method="post" th:object="${asignacionDTO}">
                            <input type="hidden" name="fechaSemana" th:value="${fechaActual}">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="coordinadorId" class="form-label">Coordinador:</label>
                                        <select th:field="*{coordinadorId}" class="form-select" id="coordinadorId" required>
                                            <option value="">Seleccione un coordinador</option>
                                            <option th:each="coordinador : ${coordinadores}" 
                                                    th:value="${coordinador.id}" 
                                                    th:text="${coordinador.nombres + ' ' + coordinador.apellidos}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="espacioId" class="form-label">Espacio:</label>
                                        <select th:field="*{espacioId}" class="form-select" id="espacioId" required>
                                            <option value="">Seleccione un espacio</option>
                                            <option th:each="espacio : ${espacios}" 
                                                    th:value="${espacio.id}" 
                                                    th:text="${espacio.nombre + ' - ' + espacio.ubicacion}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="fechaInicio" class="form-label">Fecha Inicio:</label>
                                        <input type="date" th:field="*{fechaInicioSemana}" class="form-control" 
                                               id="fechaInicio" th:value="${calendario.fechaInicioSemana}" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="fechaFin" class="form-label">Fecha Fin:</label>
                                        <input type="date" th:field="*{fechaFinSemana}" class="form-control" 
                                               id="fechaFin" th:value="${calendario.fechaFinSemana}" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="horaEntrada" class="form-label">Hora Entrada:</label>
                                        <input type="time" th:field="*{horaEntrada}" class="form-control" id="horaEntrada" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="horaSalida" class="form-label">Hora Salida:</label>
                                        <input type="time" th:field="*{horaSalida}" class="form-control" id="horaSalida" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="observaciones" class="form-label">Observaciones:</label>
                                <textarea th:field="*{observaciones}" class="form-control" id="observaciones" 
                                          rows="2" placeholder="Observaciones adicionales (opcional)"></textarea>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save me-2"></i>Asignar Coordinador
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Información de Coordinadores -->
                <div class="col-md-4">
                    <div class="coordinador-info">
                        <h5 class="mb-3">
                            <i class="fa fa-users me-2"></i>
                            Coordinadores Activos
                        </h5>
                        
                        <div th:each="coordinadorInfo : ${calendario.coordinadores.values()}" class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong th:text="${coordinadorInfo.nombreCompleto}"></strong><br>
                                    <small th:text="${coordinadorInfo.correo}"></small>
                                </div>
                            </div>
                            <div class="estadisticas-coordinador">
                                <div class="estadistica">
                                    <div class="numero" th:text="${coordinadorInfo.totalHorasAsignadas}">0</div>
                                    <div class="label">Horas</div>
                                </div>
                                <div class="estadistica">
                                    <div class="numero" th:text="${coordinadorInfo.totalEspaciosAsignados}">0</div>
                                    <div class="label">Espacios</div>
                                </div>
                            </div>
                        </div>
                        
                        <div th:if="${#lists.isEmpty(calendario.coordinadores)}">
                            <p class="text-muted text-center">
                                <i class="fa fa-info-circle"></i>
                                No hay coordinadores asignados esta semana
                            </p>
                        </div>
                    </div>
                    
                    <!-- Reservas sin coordinador -->
                    <div th:if="${!#lists.isEmpty(reservasSinCoordinador)}" class="coordinador-info">
                        <h5 class="mb-3 text-danger">
                            <i class="fa fa-exclamation-triangle me-2"></i>
                            Reservas sin Coordinador
                        </h5>
                        
                        <div th:each="reserva : ${reservasSinCoordinador}" class="reserva-item reserva-sin-coordinador mb-2">
                            <strong th:text="${reserva.usuario.nombres + ' ' + reserva.usuario.apellidos}"></strong><br>
                            <small th:text="${reserva.espacioDeportivo.nombre}"></small><br>
                            <small>
                                <i class="fa fa-calendar"></i>
                                <span th:text="${#temporals.format(reserva.fechaReserva, 'dd/MM/yyyy')}"></span>
                                <i class="fa fa-clock ms-2"></i>
                                <span th:text="${#temporals.format(reserva.horario.horaInicio, 'HH:mm')}"></span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <footer class="main-footer">
        &copy; <script>document.write(new Date().getFullYear())</script> 
        <a href="https://munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. 
        Todos los derechos reservados.
    </footer>
</div>

<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script th:src="@{/js/template.js}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mostrar mensajes de éxito o error
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.has('success')) {
        const success = urlParams.get('success');
        if (success === 'asignacion_exitosa') {
            alert('Coordinador asignado exitosamente');
        }
    }
    
    if (urlParams.has('error')) {
        const error = urlParams.get('error');
        switch(error) {
            case 'campos_obligatorios':
                alert('Todos los campos son obligatorios');
                break;
            case 'conflicto_horario':
                alert('El coordinador ya tiene una asignación en ese horario');
                break;
            case 'error_interno':
                alert('Error interno del servidor');
                break;
        }
    }
    
    // Auto-completar fechas de la semana
    const fechaInicio = document.getElementById('fechaInicio');
    const fechaFin = document.getElementById('fechaFin');
    
    if (fechaInicio && fechaFin) {
        fechaInicio.addEventListener('change', function() {
            const fecha = new Date(this.value);
            const lunes = new Date(fecha);
            lunes.setDate(fecha.getDate() - fecha.getDay() + 1);
            
            const domingo = new Date(lunes);
            domingo.setDate(lunes.getDate() + 6);
            
            fechaInicio.value = lunes.toISOString().split('T')[0];
            fechaFin.value = domingo.toISOString().split('T')[0];
        });
    }
});

feather.replace();
</script>

</body>
</html>
