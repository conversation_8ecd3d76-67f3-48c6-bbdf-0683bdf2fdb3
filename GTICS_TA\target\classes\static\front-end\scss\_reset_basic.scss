/*---reset---*/
/*Body*/
html{
    height: $p100;
    font-size: 14px;
}
body{
    height: $p100;
    &.layout-boxed{
        height: $p100;
    }
}
.wrapper{
    height: $p100;
    overflow-x: hidden;
    overflow-y: hidden;
    background-color: transparent;
    position: relative;
    @include before-after-state{
        content: " ";
        display: table;
    }
}
body{
    overflow-x: hidden;
    overflow-y: auto;
	color:$dark;
	font-size: 1rem;
	font-style: normal;
	font-weight:400;
	font-family: $bodyfont;
	line-height:1.5;
	background-color: #fafbfd;
}

@include screen-lg {
    .container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
        padding: 0 ($default-gutter-width / 2);
    }
}
@include screen-md-max {    
    .container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
        padding: 0 ($default-gutter-width / 2);
    }
}

/*---basic---*/
a {
    color: $dark;
}
a{
    @include hover-state{
        outline: 0;
        text-decoration: none;   
    }
}
h1,h2,h3,h4,h5,h6,.h1, .h2, .h3, .h4, .h5, .h6 {
	
	font-family: $headingfont;
    font-weight: 400;
    line-height: 1.2;
}
h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small {
    font-size: 65%;
}
h4, .h4, h5, .h5, h6, .h6 {
    margin-bottom: 0.7142857143rem;
}
.h1, h1 {
    font-size: 2.5714285714285716rem;
}

.h2, h2 {
    font-size: 2.142857142857143rem;
}

.h3, h3 {
    font-size: 1.7142857142857142rem;
}

.h4, h4 {
    font-size: 1.2857142857142858rem;
}

.h5, h5 {
    font-size: 1.1428571428571428rem;
}

.h6, h6 {
    font-size: 1rem;
}
h1, h2, h3, .h1, .h2, .h3 {
    margin-top: 0.7142857143rem;
    margin-bottom: 0.7142857143rem;
}
img {
    max-width: $p100;
}
.img-fluid{
	width: $p100;
}
.align-sub {
    vertical-align: sub;
}
.base-font{font-family: $bodyfont !important; }
.heading-font{font-family: $headingfont !important; }
$font-size-map: ( fs-: font-size);
$sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 72 76 78 80 100 140 180 200;
@each $size in $sizes-list {
    $val: $size / 14 + rem + !important;
    @each $keyword,
    $property in $font-size-map {
        .#{$keyword}#{$size} {
            #{$property}: $val;
        }
    }
}

$line-height-map: ( l-h-: line-height);
$sizes-list: 0 10 12 14 16 18 20 22 24 25 26 30 32 35 36 38 40 42 45 46 48 50 60 70 72 76 78 80 100 140 180 200;
@each $size in $sizes-list {
    $val: $size / 14 + rem + !important;
    @each $keyword,
    $property in $line-height-map {
        .#{$keyword}#{$size} {
            #{$property}: $val;
        }
    }
}

.l-h-n{
    line-height: normal !important;
}

$font-weight-map: ( fw-: font-weight);
$sizes-list: 100 200 300 400 500 600 700 800 900;
@each $size in $sizes-list {
    $val: $size;
    @each $keyword,
    $property in $font-weight-map {
        .#{$keyword}#{$size} {
            #{$property}: $val;
        }
    }
}
.fw-light {
    font-weight: $fw-light;
}
.fw-normal {
    font-weight: $fw-normal;
}
.fw-medium {
    font-weight: $fw-medium;
}
.fw-bold {
    font-weight: $fw-bold;
}

.hide {
    display: none !important;
}
.no-border {
    border: 0 !important;
}
.no-radius {
    border-radius: 0 !important;
}
.no-padding {
    padding: 0 !important;
}
.no-margin {
    margin: 0 !important;
}
.no-shadow {
    box-shadow: none !important;
}
.chart-legend, .contacts-list, .list-unstyled, .mailbox-attachments, .users-list {
    list-style: none;
    margin: 0;
    padding: 0;
}
.list-group-unbordered>.list-group-item {
    border-left: 0;
    border-right: 0;
    border-radius: 0;
    padding-left: 0;
    padding-right: 0;
}
.code-preview {
    border: 1px solid $light;
    padding: 20px;
    background-color: $white;
}
code {
    border: 1px solid $light;
    background-color: $white;
    border-radius: .25rem;
    padding: .2rem .4rem;
}
hr {
    background-color: rgba(lighten($black, 15%),0.07);
    margin: 1rem auto;
    opacity: 1;
}
.d3-line{
	fill:rgba(255, 255, 255, 0);
}

.theme-switch li a.active{
	opacity: 0.6;
    border: 5px solid #ffffff;
}
.dark-skin .theme-switch li a.active{
    border: 5px solid lighten($black, 15%);
}
.custom-select{
	cursor: pointer;
	border-radius: $fct-border-radius;
}

.image-popup-vertical-fit img,
.image-popup-fit-width img,
.image-popup-no-margins img,
.popup-gallery img,
.zoom-gallery img,
#image-popups img{
	border-radius: $default-border-radius;
}

/*---text-white---*/
$text-white-map: ( text-white-: color);
$sizes-list: 10 20 30 40 50 60 70 80 90;
@each $size in $sizes-list {
    $val: rgba($white, $size/100) !important;
    @each $keyword,
    $property in $text-white-map{
        .#{$keyword}#{$size} {
            #{$property}: $val;
        }
    }
}

/*---text-black---*/
$text-black-map: ( text-black-: color);
$sizes-list: 10 20 30 40 50 60 70 80 90;
@each $size in $sizes-list {
    $val: rgba($black, $size/100) !important;
    @each $keyword,
    $property in $text-black-map{
        .#{$keyword}#{$size} {
            #{$property}: $val;
        }
    }
}



/*---border radius---*/
$border-radius-map: ( rounded: border-radius);
$sizes-list: 0 5 10 15 20 25 30 35 40 45 50 55 60 65 70 75 80 85 90 95 100;
@each $size in $sizes-list {
    $val: $size + px;
    @each $keyword,
    $property in $border-radius-map {
        .#{$keyword}#{$size} {
            #{$property}: $val;
        }
    }
}

.overflow-h{
	overflow: hidden;
}
.overflow-v{
	overflow: visible;
}
.overflow-xh{
	overflow-x: hidden;
}
.overflow-xv{
	overflow-x: visible;
}
.overflow-yh{
	overflow-y: hidden;
}
.overflow-yv{
	overflow-y: visible;
}

.text-overflow-h{
    text-overflow: ellipsis;
}


/*---border---*/	
$border-sizes-list: 0 1 2 3 4 5;	
@each $size in $border-sizes-list {	
  .b-#{$size}  { border:        #{$size}px solid $light !important; } // All sides	
  .bt-#{$size} { border-top:    #{$size}px solid $light !important; }	
  .be-#{$size} { border-right:  #{$size}px solid $light !important; }	
  .bb-#{$size} { border-bottom: #{$size}px solid $light !important; }	
  .bs-#{$size} { border-left:   #{$size}px solid $light !important; }	
  // Axes	
  .bx-#{$size} {	
    border-right:  #{$size}px solid $light !important;	
    border-left:   #{$size}px solid $light !important;	
  }	
  .by-#{$size} {	
    border-top:    #{$size}px solid $light !important;	
    border-bottom: #{$size}px solid $light !important;	
  }	
}	
.rtl{	
    @each $size in $border-sizes-list {	
      .bs-#{$size} { border-right-width:  #{$size}px !important; border-right-style:  solid !important; border-left-width:  0 !important; }	
      .be-#{$size} { border-left-width:   #{$size}px !important; border-left-style:  solid !important; border-right-width:  0 !important; }	
    }	
}	
.dark-skin{	
    @each $size in $border-sizes-list {	
      .b-#{$size}:not([class*=border-])  { border:        #{$size}px solid $dark2 !important; } // All sides	
      .bt-#{$size}:not([class*=border-]) { border-top:    #{$size}px solid $dark2 !important; }	
      .be-#{$size}:not([class*=border-]) { border-right:  #{$size}px solid $dark2 !important; }	
      .bb-#{$size}:not([class*=border-]) { border-bottom: #{$size}px solid $dark2 !important; }	
      .bs-#{$size}:not([class*=border-]) { border-left:   #{$size}px solid $dark2 !important; }	
      // Axes	
      .bx-#{$size}:not([class*=border-]) {	
        border-right:  #{$size}px solid $dark2 !important;	
        border-left:   #{$size}px solid $dark2 !important;	
      }	
      .by-#{$size}:not([class*=border-]) {	
        border-top:    #{$size}px solid $dark2 !important;	
        border-bottom: #{$size}px solid $dark2 !important;	
      }	
    }	
    &.rtl{	
        @each $size in $border-sizes-list {	
          .bs-#{$size}:not([class*=border-]) { border-right-width:  #{$size}px !important; border-right-style:  solid !important; border-left-width:  0 !important; }	
          .be-#{$size}:not([class*=border-]) { border-left-width:   #{$size}px !important; border-left-style:  solid !important; border-right-width:  0 !important; }	
        }	
    }	
}

.border {
  border: 1px solid $light !important;
}

// Border colors
//
@each $name, $value in $colors {
  .border-#{$name} {
    border-color: $value !important;
  }
}
.border-transparent {border-color: transparent !important; }
.border-white       {border-color: #fff !important; }
.border-light       {border-color: $light !important; }
.border-fade        {border-color: lighten($light, 01%) !important; }


.b-dashed{
	border-style: dashed !important;
}
.bt-dashed{
	border-top-style: dashed !important;
}
.bs-dashed{
	border-left-style: dashed !important;
}
.be-dashed{
	border-right-style: dashed !important;
}
.bb-dashed{
	border-bottom-style: dashed !important;
}

.b-dotted{
	border-style: dotted !important;
}
.bt-dotted{
	border-top-style: dotted !important;
}
.bs-dotted{
	border-left-style: dotted !important;
}
.be-dotted{
	border-right-style: dotted !important;
}
.bb-dotted{
	border-bottom-style: dotted !important;
}

.b-double{
	border-style: double !important;
}
.bt-double{
	border-top-style: double !important;
}
.bs-double{
	border-left-style: double !important;
}
.be-double{
	border-right-style: double !important;
}
.bb-double{
	border-bottom-style: double !important;
}

.b-groove{
	border-style: groove !important;
}
.bt-groove{
	border-top-style: groove !important;
}
.bs-groove{
	border-left-style: groove !important;
}
.be-groove{
	border-right-style: groove !important;
}
.bb-groove{
	border-bottom-style: groove !important;
}
	.bter-0{	
	border-top-right-radius: 0 !important;	
}	
.bber-0{	
	border-bottom-right-radius: 0 !important;	
}	
.btsr-0{	
	border-top-left-radius: 0 !important;	
}	
.bbsr-0{	
	border-bottom-left-radius: 0 !important;	
}	
.ber-0{	
	border-top-right-radius: 0 !important;	
	border-bottom-right-radius: 0 !important;	
}	
.bsr-0{	
	border-top-left-radius: 0 !important;	
	border-bottom-left-radius: 0 !important;	
}
.bar-0{
	border-radius: 0 !important;
}
/*---vertical-align---*/
.vertical-align{
    font-size: 0;
}
.vertical-align:before{
    display: inline-block;
    height: 100%;
    content: ''; 
    vertical-align: middle;
}
.vertical-align-middle, .vertical-align-bottom{
    display: inline-block;
    max-width: 100%;
}
.vertical-align-middle{
    vertical-align: middle;
}
.vertical-align-bottom{
    vertical-align: bottom;
}
.nowrap{ white-space: nowrap;}
.r-0{right:0}
.l-0{left:0}

.bg-none{ background: none !important};

// max-width 1199
@include screen-lg-max {
	.bg-none-lg{ background: none !important};
}
// max-width 1024

@include screen-tl {
	.bg-none-tl{ background: none !important};
}
// max-width 991

@include screen-md-max {
	.bg-none-md{ background: none !important};
}
// max-width 767

@include screen-sm-max {
	.bg-none-sm{ background: none !important};
}
// max-width 575
@include screen-xs {
	.bg-none-xs{ background: none !important};
}
// max-width 370
@include screen-small {
	.bg-none-small{ background: none !important};
}
.input-group-prepend, .input-group-append {	
    display: flex;	
}



// max-width 575
@include screen-xs {
    .xs-nowrap{white-space: nowrap;} 
    .xs-position-static {position: static !important; }
    .xs-position-relative {position: relative !important; }
    .xs-position-absolute {position: absolute !important; }
    .xs-position-fixed {position: fixed !important; }
    .xs-position-sticky {position: sticky !important; }
    
    $right-property-map: ( xs-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( xs-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( xs-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( xs-l-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// min-width 576
@include screen-sm {
    .sm-nowrap{white-space: nowrap;}
    .sm-position-static {position: static !important; }
    .sm-position-relative {position: relative !important; }
    .sm-position-absolute {position: absolute !important; }
    .sm-position-fixed {position: fixed !important; }
    .sm-position-sticky {position: sticky !important; }  
    
    $right-property-map: ( sm-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( sm-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( sm-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( sm-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// max-width 767
@include screen-sm-max {
    .sm-max-nowrap{white-space: nowrap;} 
    .sm-max-position-static {position: static !important; }
    .sm-max-position-relative {position: relative !important; }
    .sm-max-position-absolute {position: absolute !important; }
    .sm-max-position-fixed {position: fixed !important; }
    .sm-max-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( sm-max-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( sm-max-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( sm-max-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( sm-max-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// min-width 768
@include screen-md {
    .md-nowrap{white-space: nowrap;} 
    .md-position-static {position: static !important; }
    .md-position-relative {position: relative !important; }
    .md-position-absolute {position: absolute !important; }
    .md-position-fixed {position: fixed !important; }
    .md-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( md-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( md-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( md-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( md-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// max-width 991
@include screen-md-max {
    .md-max-nowrap{white-space: nowrap;} 
    .md-max-position-static {position: static !important; }
    .md-max-position-relative {position: relative !important; }
    .md-max-position-absolute {position: absolute !important; }
    .md-max-position-fixed {position: fixed !important; }
    .md-max-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( md-max-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( md-max-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( md-max-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( md-max-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// min-width 992
@include screen-lg {
    .lg-nowrap{white-space: nowrap;} 
    .lg-position-static {position: static !important; }
    .lg-position-relative {position: relative !important; }
    .lg-position-absolute {position: absolute !important; }
    .lg-position-fixed {position: fixed !important; }
    .lg-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( lg-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( lg-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( lg-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( lg-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// max-width 1024
@include screen-tl {
    .tl-nowrap{white-space: nowrap;} 
    .tl-position-static {position: static !important; }
    .tl-position-relative {position: relative !important; }
    .tl-position-absolute {position: absolute !important; }
    .tl-position-fixed {position: fixed !important; }
    .tl-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( tl-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( tl-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( tl-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( tl-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// max-width 1199
@include screen-lg-max {
    .lg-max-nowrap{white-space: nowrap;} 
    .lg-max-position-static {position: static !important; }
    .lg-max-position-relative {position: relative !important; }
    .lg-max-position-absolute {position: absolute !important; }
    .lg-max-position-fixed {position: fixed !important; }
    .lg-max-position-sticky {position: sticky !important; }
    
    $right-property-map: ( lg-max-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( lg-max-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( lg-max-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( lg-max-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// Extra large devices  (min-width 1200)
@include screen-xl {
    .xl-nowrap{white-space: nowrap;} 
    .xl-position-static {position: static !important; }
    .xl-position-relative {position: relative !important; }
    .xl-position-absolute {position: absolute !important; }
    .xl-position-fixed {position: fixed !important; }
    .xl-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( xl-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( xl-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( xl-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( xl-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

// Extra large devices  (min-width 1400)
@include screen-xxl {
    .xxl-nowrap{white-space: nowrap;} 
    .xxl-position-static {position: static !important; }
    .xxl-position-relative {position: relative !important; }
    .xxl-position-absolute {position: absolute !important; }
    .xxl-position-fixed {position: fixed !important; }
    .xxl-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( xxl-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( xxl-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( xxl-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( xxl-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}


// Extra large devices  (min-width 1599)
@include screen-xxxl {
    .xxxl-nowrap{white-space: nowrap;} 
    .xxxl-position-static {position: static !important; }
    .xxxl-position-relative {position: relative !important; }
    .xxxl-position-absolute {position: absolute !important; }
    .xxxl-position-fixed {position: fixed !important; }
    .xxxl-position-sticky {position: sticky !important; } 
    
    $right-property-map: ( xxxl-r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( xxxl-l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( xxxl-t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( xxxl-b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
}

    $right-property-map: ( r-: right);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $right-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $left-property-map: ( l-: left);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $left-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $top-property-map: ( t-: top);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $top-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }
    
    $bottom-property-map: ( b-: bottom);
    $sizes-list: 0 10 12 14 16 18 20 22 24 26 30 32 36 38 40 42 46 48 50 60 70 80 90 100 110 120 130 140 150;
    @each $size in $sizes-list {
        $val: $size / 14 + rem + !important;
        @each $keyword,
        $property in $bottom-property-map {
            .#{$keyword}#{$size} {
                #{$property}: $val;
            }
        }
    }

button:focus {
    outline: none;
}
.media {	
  display: flex;	
  align-items: flex-start;	
}	
.media-body {	
  flex: 1;	
}	
small, .small {	
    font-weight: 400;	
}