/* 
 * <AUTHOR> <<EMAIL>>
 * @link      http://www.themepunch.com/
 * @copyright 2017 ThemePunch
*/

;(function(){function k(a){this.slider=a;this.slides=[];this.panos=a.find("li[data-panorama]").each(function(a){0===a&&this.setAttribute("data-fstransition","notransition");this.setAttribute("data-transition","fade")});a.one("revolution.slide.onloaded",this.onLoaded.bind(this)).one("rsPanoramaDestroyed",this.destroy.bind(this))}function l(a,b,c,d){var e=JSON.parse(a.attr("data-panorama"));this.y=this.x=0;this.zoomVal=1;this.width=c;this.height=d;this.slide=a;this.slider=b;this.distY=this.distX=!1;
this.image=e.image;this.controls=e.controls;this.drag="drag"===this.controls;this.thrw="throw"===this.controls;this.move=this.drag||this.thrw;this.mouse="mouse"===this.controls;this.click="click"===this.controls;this.autoplay="true"==e.autoplay;this.zoom="true"==e.mousewheelZoom;this.smoothZoom="true"==e.smoothZoom;b="forward"===e.autoplayDirection?1:-1;this.autoplaySpeed=.001*parseInt(e.autoplaySpeed,10);this.speed=this.autoplaySpeed*b;this.throwSpeed=.001*parseInt(e.throwSpeed,10);this.zoomMax=
2-.01*parseInt(e.zoomMax,10);this.zoomMin=2-.01*parseInt(e.zoomMin,10);this.fov=parseInt(e.cameraFov,10);this.onStart=this.start.bind(this);this.onZoom=this.zooming.bind(this);this.inZoom=this.zoomIn.bind(this);this.outZoom=this.zoomOut.bind(this);this.onRender=this.render.bind(this);this.onMouse=this.mouseEvent.bind(this);this.onImgChange=this.imgChange.bind(this);this.fireAction=this.onAction.bind(this);this.renderer=new THREE[g];this.renderer.setSize(c,d);this.camera=new THREE.PerspectiveCamera(this.fov,
c/d,1,parseInt(e.cameraFar,10));this.camera.target=new THREE.Vector3(0,0,0);c=new THREE.SphereGeometry(parseInt(e.sphereRadius,10),parseInt(e.sphereWsegments,10),parseInt(e.sphereHsegments,10));this.texture=new THREE.TextureLoader;this.texture.minFilter=THREE.LinearFilter;c.applyMatrix((new THREE.Matrix4).makeScale(-1,1,1));this.material=new THREE.MeshBasicMaterial;this.scene=new THREE.Scene;this.scene.add(new THREE.Mesh(c,this.material));f.data(a[0],"rsaddonpanorama",this);this.canvas=f(this.renderer.domElement).addClass("rsaddon-panorama").appendTo(a.find(".slotholder"));
a.on("mouseleave",this.onLeave.bind(this)).find(".tp-withaction").each(m)}function n(a){a.stopPropagation()}function m(){var a=jQuery(this),b=a.data().actions;if(!a.hasClass("rspanoaction")&&Array.isArray(b))for(var c=b.length,d;c--;)if(d=b[c],-1!==d.action.search("panorama"))a.addClass("rspanoaction").on("mousedown click mousemove touchmove",n)}function p(){try{var a=document.createElement("canvas");(a=!(!window.WebGLRenderingContext||!a.getContext("webgl")&&!a.getContext("experimental-webgl")))&&
(a="WebGLRenderer")}catch(b){}a||(a=window.CanvasRenderingContext2D)&&(a="CanvasRenderer");return a}var f,g,h=window.requestAnimationFrame;window.RsAddonPanorama=function(a,b){if(!g&&(g=p(),!g)){b.addClass("pano-no-webgl");return}if(a&&b&&"undefined"!==typeof THREE&&h&&b.find("li[data-panorama]").length)return f=a,f.event.special.rsPanoramaDestroyed={remove:function(a){a.handler()}},new k(b)};k.prototype={onLoaded:function(){this.slider.one("revolution.slide.onchange",this.onReady.bind(this));jQuery(window).on("blur.rsaddonpanorama",
this.onBlur.bind(this))},onReady:function(a,b){this.resize();var c=this;this.panos.each(function(a){c.slides[a]=new l(f(this),c.slider,c.width,c.height)});this.slider.on("revolution.slide.afterdraw",this.resize.bind(this)).on("revolution.slide.onbeforeswap",this.beforeSwap.bind(this)).on("revolution.slide.onafterswap",this.afterSwap.bind(this)).on("layeraction",this.layerAction.bind(this));var d=f.data(b.currentslide[0],"rsaddonpanorama");if(d)d.onReset();this.panos=null},beforeSwap:function(a,b){var c=
f.data(b.currentslide[0],"rsaddonpanorama");c&&c.controls&&c.removeEvents();if(c=f.data(b.nextslide[0],"rsaddonpanorama"))c.onReset()},afterSwap:function(a,b){this.currentSlide=f.data(b.currentslide[0],"rsaddonpanorama");if(b.prevslide.length){var c=f.data(b.prevslide[0],"rsaddonpanorama");c&&(c.paused=!0)}},resize:function(){this.width=this.slider.width();this.height=this.slider.height();for(var a=this.slides.length,b;a--;)b=this.slides[a],b.resize(this.width,this.height)},onBlur:function(){for(var a=
this.slides.length,b;a--;)b=this.slides[a],b.clear(),b.canvas.trigger("mouseup").trigger("mouseleave").trigger("touchend")},layerAction:function(a,b){b=b.event;var c=b.action;if(c&&-1!==c.search("panorama")&&this.currentSlide){var d=b.hasOwnProperty("percentage")?.01*b.percentage:!1;this.currentSlide.action(c.replace("panorama_",""),d)}},destroy:function(){if(this.slides)for(;this.slides.length;)this.slides[0].destroy(),this.slides.shift();for(var a in this)this.hasOwnProperty(a)&&delete this[a]}};
l.prototype={start:function(){this.imgLoaded||(this.imgLoaded=!0);this.currentSlide&&(this.newImage&&(this.y=this.x=0,this.zoomVal=1,this.camera.fov=this.fov,this.camera.updateProjectionMatrix(),this.newImage=!1),this.paused=this.controller=!1,h(this.onRender),this.addEvents(),this.running=!0,this.slider.addClass("rsaddon-pano"))},render:function(){if(!this.paused&&this.camera){this.y=Math.max(-85,Math.min(85,this.y));if(!1!==this.distX||!1!==this.distY)!1!==this.distX?(this.x+=.05*(this.distX-this.x),
1>Math.abs(this.distX-this.x)&&(this.distX=!1,clearInterval(this.actionTimer))):!1!==this.distY&&(this.y+=.05*(this.distY-this.y),1>Math.abs(this.distY-this.y)&&(this.distY=!1,clearInterval(this.actionTimer)));else if(this.controller)if(this.move)this.thrw&&(this.mousedown?(this.vx=this.x-this.oldX,this.vy=this.y-this.oldY,this.oldX=this.x,this.oldY=this.y):(a=Math.abs(this.vx),b=Math.abs(this.vy),.01<a&&(this.x+=this.vx),.01<b&&(this.y+=this.vy),this.vx*=this.throwSpeed,this.vy*=this.throwSpeed,
.01>=a&&.01>=b&&(this.controller=!1)));else{var a=(this.mouseX-this.left)/this.width,b=(this.mouseY-this.top)/this.height,a=.5>=a?-180*(1-2*a):360*(a-.5),b=Math.max(-85,Math.min(85,.5>=b?85*(1-2*b):-170*(b-.5)));this.x+=.05*(a-this.x);this.y+=.05*(b-this.y);(this.click||this.mouse)&&1>Math.abs(a-this.x)&&1>Math.abs(b-this.y)&&(this.controller=!1)}else this.autoplay&&(this.x+=this.speed);a=THREE.Math.degToRad(this.x);b=THREE.Math.degToRad(90-this.y);this.camera.target.x=Math.sin(b)*Math.cos(a);this.camera.target.z=
Math.sin(b)*Math.sin(a);this.camera.target.y=Math.cos(b);this.camera.lookAt(this.camera.target);this.smoothZoom&&(this.camera.fov+=.05*(this.fov*this.zoomVal-this.camera.fov),this.camera.updateProjectionMatrix());this.renderer.render(this.scene,this.camera);h(this.onRender)}},onLeave:function(){clearInterval(this.actionTimer);clearInterval(this.zoomTimer)},clear:function(){clearInterval(this.actionTimer);clearInterval(this.zoomTimer);this.distY=this.distX=!1},onAction:function(){this.action(this.actionType,
this.actionPerc)},action:function(a,b){switch(a){case "left":this.distX=this.x+-180*b;break;case "right":this.distX=this.x+180*b;break;case "up":var c=this.y+85*b;this.distY=Math.max(-85,Math.min(85,c));break;case "down":c=this.y+-85*b;this.distY=Math.max(-85,Math.min(85,c));break;case "leftstart":var d="left";break;case "rightstart":d="right";break;case "upstart":d="up";break;case "downstart":d="down";break;case "leftend":case "rightend":case "upend":case "downend":clearInterval(this.actionTimer);
break;case "zoomin":clearInterval(this.zoomTimer);this.zooming("gestureend",.5);break;case "zoomout":clearInterval(this.zoomTimer);this.zooming("gestureend",1.5);break;case "zoominstart":clearInterval(this.zoomTimer);this.inZoom();this.zoomTimer=setInterval(this.inZoom,100);break;case "zoomoutstart":clearInterval(this.zoomTimer);this.outZoom();this.zoomTimer=setInterval(this.outZoom,100);break;case "zoominend":case "zoomoutend":clearInterval(this.zoomTimer)}d&&(this.clear(),this.actionPerc=b,this.actionType=
d,this.fireAction(),this.actionTimer=setInterval(this.fireAction,100))},zooming:function(a,b){this.prevZoom=this.zoomVal;if("gestureend"!==a){var c=a.type;b=a.scale}else c=a;this.zoomVal="gestureend"!==c?0<a.originalEvent.wheelDelta||0>a.originalEvent.detail?this.zoomVal-.05:this.zoomVal+.05:1>b?this.zoomVal-.05:this.zoomVal+.05;this.zoomVal=Math.max(this.zoomMax,Math.min(this.zoomMin,this.zoomVal));this.zoomVal!==this.zoomMax&&this.zoomVal!==this.zoomMin||clearInterval(this.zoomTimer);this.smoothZoom||
(this.camera.fov=this.fov*this.zoomVal,this.camera.updateProjectionMatrix());return!1},zoomIn:function(){this.zooming("gestureend",.5)},zoomOut:function(){this.zooming("gestureend",1.5)},imgChange:function(){this.timer=setTimeout(this.onStart,500)},update:function(a,b,c){clearTimeout(this.timer);this.removeEvents();this.paused=!0;switch(a){case "image":this.newImage=!0;this.material.map=this.texture.load(b,this.onImgChange);break;case "controls":this.controls=b;"none"!==b?(this.drag="drag"===b,this.thrw=
"throw"===b,this.mouse="mouse"===b,this.click="click"===b,this.move=this.drag||this.thrw):this.y=this.x=0;break;case "autoplay":this.autoplay="true"==b;break;case "direction":this.speed=this.autoplaySpeed*("forward"===b?1:-1);break;case "zoom":"false"==b?(delete this.zoom,this.zoomVal=1,this.camera.fov=this.fov,this.camera.updateProjectionMatrix()):this.zoom=!0;break;case "smooth":this.smoothZoom="true"==b}"image"!==a&&(this.timer=setTimeout(this.onStart,500))},resize:function(a,b){this.width=a;this.height=
b;this.renderer.setSize(a,b);this.camera.aspect=a/b;this.camera.updateProjectionMatrix()},onReset:function(){this.currentSlide=this.paused=!0;this.y=this.x=0;this.distY=this.distX=!1;this.zoomVal=1;this.camera.fov=this.fov;this.camera.updateProjectionMatrix();this.imgLoaded?this.start():this.material.map=this.texture.load(this.image,this.onStart)},enterMouse:function(a,b){this.move&&this.canvas.removeClass("rsaddon-panorama-drag").addClass("rsaddon-panorama-dragging");this.clear();this.mousedown=
this.controller=!0;var c=this.slider.offset();this.left=c.left;this.top=c.top;b||((c=a.originalEvent.touches)&&(a=c[0]),this.startX=a.pageX-this.left,this.startY=a.pageY-this.top,this.prevX=this.x,this.prevY=this.y,this.oldX=this.x,this.oldY=this.y,this.canvas.on("mousemove.rsaddonpanorama touchmove.rsaddonpanorama",this.onMouse))},mouseEvent:function(a){this.onLeave();if(-1!==a.type.search(/move|click/)){if(this.move){if(!this.controller||!this.mousedown)return}else this.controller||this.enterMouse(a,
!0);var b=a.originalEvent.touches;b&&(a=b[0]);this.move?(this.x=this.prevX+.1*(this.startX-(a.pageX-this.left)),this.y=this.prevY+.1*(a.pageY-this.top-this.startY)):(this.mouseX=a.pageX,this.mouseY=a.pageY)}else-1!==a.type.search(/down|enter|start/)?this.enterMouse(a):(this.canvas.off("mousemove.rsaddonpanorama touchmove.rsaddonpanorama"),this.drag&&(this.controller=!1),this.mousedown=!1,this.move&&this.canvas.removeClass("rsaddon-panorama-dragging").addClass("rsaddon-panorama-drag"))},addEvents:function(){switch(this.controls){case "drag":case "throw":this.canvas.on("mousedown.rsaddonpanorama mouseup.rsaddonpanorama mouseleave.rsaddonpanorama touchstart.rsaddonpanorama touchend.rsaddonpanorama touchcancel.rsaddonpanorama",
this.onMouse);break;case "click":this.slide.on("click.rsaddonpanorama",this.onMouse);break;case "mouse":this.slide.on("mousemove.rsaddonpanorama touchmove.rsaddonpanorama",this.onMouse)}if(this.zoom)this.slider.on("mousewheel.rsaddonpanorama DOMMouseScroll.rsaddonpanorama gestureend.rsaddonpanorama",this.onZoom);"none"!==this.controls&&(this.move?this.canvas.addClass("rsaddon-panorama-drag"):this.click&&this.slide.addClass("rsaddon-panorama-click"))},removeEvents:function(){this.clear();this.currentSlide=
this.controller=this.mousedown=!1;switch(this.controls){case "drag":case "throw":this.canvas.off(".rsaddonpanorama");break;case "mouse":case "click":this.slide.off(".rsaddonpanorama")}this.slider.off(".rsaddonpanorama");this.slide.removeClass("rsaddon-panorama-click");this.canvas.removeClass("rsaddon-panorama-drag rsaddon-panorama-dragging")},destroy:function(){for(var a in this)this.hasOwnProperty(a)&&delete this[a]}}})();







