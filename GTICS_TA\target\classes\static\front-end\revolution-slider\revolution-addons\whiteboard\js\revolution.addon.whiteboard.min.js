/********************************************
 * REVOLUTION 5.0+ EXTENSION - WHITEBOARD
 * @version: 2.0 (20.10.2016)
 * @requires jquery.themepunch.revolution.js
 * <AUTHOR>
*********************************************/
!function(a){var b=jQuery.fn;jQuery.extend(!0,b,{rsWhiteBoard:function(a){return this.each(function(){var a=this.opt;jQuery(this).on("revolution.slide.onloaded",function(){c(a)})})}});var c=function(a){if(o=a.whiteboard,void 0===o)return!1;var b="position:absolute;z-index:1000;top:0px;left:0px;",c="position:absolute;z-index:1000;top:0px;left:0px;",d="position:absolute;z-index:1000;top:0px;left:0px;",f="position:absolute; top:0px;left:0px;background-size:contain;background-repeat:no-repeat;background-position:center center;";void 0!=o.movehand&&(o.movehand.markup='<div class="wb-move-hand wb-thehand" style="'+b+'"><div class="wb-hand-inner" style="'+c+";width:"+o.movehand.width+"px;height:"+o.movehand.height+'px"><div class="wb-hand-scale" ><div class="wb-hand-image" style="'+f+"width:"+o.movehand.width+"px;height:"+o.movehand.height+"px;background-image:url("+o.movehand.src+');"></div></div></div>'),void 0!=o.writehand&&(o.writehand.markup='<div class="wb-draw-hand wb-thehand" style="'+b+'"><div class="wb-hand-inner" style="'+c+'"><div class="wb-hand-scale" style="'+d+'"><div class="wb-hand-image" style="'+f+"width:"+o.writehand.width+"px;height:"+o.writehand.height+"px;background-image:url("+o.writehand.src+');"></div></div></div>'),jQuery(window).resize(function(){clearTimeout(a.whiteboard_resize_timer),a.whiteboard_resize_timer=setTimeout(function(){jQuery(".wb-thehand").each(function(){var b=jQuery(this).find(".wb-hand-scale");punchgs.TweenLite.set(b,{scale:a.bw})})},50)}),a.c.on("revolution.slide.onbeforeswap",function(b,c){a.c.find(".wb-thehand").remove()}),a.c.on("revolution.layeraction",function(b,c){var d=c.layersettings.whiteboard;if(void 0!=d){1!=d.configured&&("write"!=d.hand_function&&"draw"!=d.hand_function||(d.jitter_distance=void 0!=d.jitter_distance?parseInt(d.jitter_distance,0)/100:parseInt(o.writehand.jittering.distance,0)/100,d.jitter_distance_horizontal=void 0!=d.jitter_distance_horizontal?parseInt(d.jitter_distance_horizontal,0)/100:parseInt(o.writehand.jittering.distance_horizontal,0)/100,d.jitter_offset=void 0!=d.jitter_offset?parseInt(d.jitter_offset,0)/100:parseInt(o.writehand.jittering.offset,0)/100,d.jitter_offset_horizontal=void 0!=d.jitter_offset_horizontal?parseInt(d.jitter_offset_horizontal,0)/100:parseInt(o.writehand.jittering.offset_horizontal,0)/100,d.hand_type=d.hand_type||o.writehand.handtype),"move"==d.hand_function&&(d.hand_type=d.hand_type||o.movehand.handtype),d.configured=!0);var f=c.layer.data(),g=jQuery(f._pw).find(".wb-thehand");if(g.length>0)var k=g.data("wb");else var k=jQuery.extend(!0,{},c.layersettings.whiteboard);if("enterstage"==c.eventtype){if(jQuery(c.layer).is(":visible")===!1)return;c.layer.hasClass("handadded")||(c.layer.addClass("handadded"),c.layersettings.handEffect="on",1!=k.handadded&&h(a,c,k),i(c,k))}"enteredstage"==c.eventtype&&c.layer.hasClass("handadded")&&!c.layer.hasClass("handremoved")&&(c.layer.addClass("handremoved"),c.layersettings.handEffect="off",0==e(a,c,k)&&j(a,c,k)),"leavestage"==c.eventtype&&(c.layer.removeClass("handadded"),c.layer.removeClass("handremoved"))}})},d=function(a){var b={},c=a.layer.data();return b.obj="",b.startat=9999999,jQuery(c._li).find(".tp-caption").each(function(){var a=jQuery(this),c=a.data();1!=c.active&&void 0!=c.whiteboard&&"move"!=c.whiteboard.hand_function&&parseInt(c.frames[0].delay,0)<b.startat&&(b.obj=a,b.startat=parseInt(c.frames[0].delay,0))}),b},e=function(a,b,c){var e=!1,f=b.layer.data(),g=jQuery(f._pw).find(".wb-thehand");if("on"===f.whiteboard.goto_next_layer&&"move"!=f.whiteboard.hand_function&&0==a.c.find(".wb-between-stations").length){var h=d(b);if(void 0!=h&&h.obj.length>0){var i=g.find(".wb-hand-inner"),j=void 0!=f.timeline&&void 0!=f.timeline._labels&&void 0!=f.timeline._labels.frame_0_end?f.timeline._labels.frame_0_end:0,k=h.startat/1e3-j,l=jQuery(f._pw).position(),m=h.obj.data("_pw").position(),n=g.closest(".tp-static-layers").length>0;g.appendTo(a.ul),g.addClass("wb-between-stations"),n?g.css({zIndex:200}):g.css({zIndex:100}),c.handEffect="off",punchgs.TweenLite.fromTo(g,k,{top:l.top,left:l.left},{top:m.top,left:m.left}),punchgs.TweenLite.to(i,k,{x:0,y:0,onComplete:function(){g.remove()}}),e=!0}}return e},f=function(a,b,c){if("off"!=c.handEffect&&1==c.handadded){var d=b.layer.data(),g=(c.maxwave||d.eoh,parseInt(c.hand_angle,0)||10);ro="write"==c.hand_function||"draw"==c.hand_function?Math.random()*g-g/2:0,c.rotatespeed=c.rotatespeed||.05,c.rotating_anim=punchgs.TweenLite.to(a,c.rotatespeed,{rotationZ:ro,ease:punchgs.Power3.easeOut,onComplete:function(){f(a,b,c)}})}},g=function(a,b,c){if("off"!=c.handEffect&&1==c.handadded){var d=b.layer.data();if("horizontal"==c.jitter_direction){var e=c.maxwave||d.eow*c.jitter_distance_horizontal,f=d.eow*c.jitter_offset_horizontal;if(0==e)return;c.current_x_offset=c.current_x_offset||0,c.lastwave=Math.random()*e+f,c.jitterspeed=c.jitterspeed||.05,c.jittering_anim=punchgs.TweenLite.to(a,c.jitterspeed,{x:c.lastwave,ease:punchgs.Power3.easeOut,onComplete:function(){g(a,b,c)}})}else{var h=c.maxwave||d.eoh;if(0==h)return;c.current_y_offset=c.current_y_offset||0,c.lastwave=c.lastwave==c.current_y_offset+h*c.jitter_offset?Math.random()*(h*c.jitter_distance)+c.current_y_offset+h*c.jitter_offset:c.current_y_offset+h*c.jitter_offset,c.jitterspeed=c.jitterspeed||.05,c.jittering_anim=punchgs.TweenLite.to(a,c.jitterspeed,{y:c.lastwave,ease:punchgs.Power3.easeOut,onComplete:function(){g(a,b,c)}})}}},h=function(a,b,c){var d="move"==c.hand_function?a.whiteboard.movehand:a.whiteboard.writehand,e=d.markup,f=b.layer.data();c.hand_full_rotation=c.hand_full_rotation||0,c.hand_origin=d.transform.transformX+"px "+d.transform.transformY+"px",c.hand_scale="right"==c.hand_type?1:-1,c.hand_x_offset=parseInt(c.hand_x_offset,0)||0,c.hand_y_offset=parseInt(c.hand_y_offset,0)||0,jQuery(e).appendTo(jQuery(f._pw)),c.handadded=!0;var g=jQuery(f._pw).find(".wb-thehand"),i=(g.find(".wb-hand-inner"),g.find(".wb-hand-image"));punchgs.TweenLite.set(i,{scaleX:c.hand_scale,rotation:c.hand_full_rotation,transformOrigin:c.hand_origin,x:0-d.transform.transformX+c.hand_x_offset,y:0-d.transform.transformY+c.hand_y_offset}),punchgs.TweenLite.set(g.find(".wb-hand-scale"),{scale:a.bw,transformOrigin:"0% 0%"})},i=function(a,b){var c=a.layer.data(),d=jQuery(c._pw).find(".wb-thehand"),e=d.find(".wb-hand-inner"),i=(d.find(".wb-hand-image"),punchgs.TweenLite.getTweensOf(a.layer));switch(b.hand_function){case"write":case"draw":var j=c.frames[a.frame_index].speed/1e3;if(void 0!=c.splittext&&"none"!=c.splittext)b.tweens=a.layersettings.timeline.getChildren(!0,!0,!1),jQuery.each(b.tweens,function(c,d){d.eventCallback("onStart",function(a,b,d){var e=jQuery(this.target),f=e.width(),g=e.height();if(void 0!==e&&void 0!==e.html()&&e.html().length>0&&9!=e.html().charCodeAt(0)&&10!=e.html().charCodeAt(0)){var h=e.position(),i=e.parent(),j=i.parent(),l=("write"==d.hand_function?10*Math.random()-5:0,this._duration),m=h.left,n=h.top;i.hasClass("tp-splitted")&&(m=i.position().left+m,n=i.position().top+n),j.hasClass("tp-splitted")&&(m=j.position().left+m,n=j.position().top+n),d.rotatespeed=void 0!==d.hand_angle_repeat?parseFloat(l)/parseFloat(d.hand_angle_repeat):l>1?l/6:l>.5?l/6:l/3,d.jitterspeed=void 0!==d.jitter_repeat?parseFloat(l)/parseFloat(d.jitter_repeat):l>1?l/6:l>.5?l/6:l/3,d.current_y_offset!=n&&(l=.1),d.current_y_offset=n||0,d.maxwave=g,c<d.tweens.length-1?punchgs.TweenLite.to(a,l,{x:m+f}):punchgs.TweenLite.to(a,l,{x:m+f,onComplete:function(){d.handEffect="off";try{d.jittering_anim.kill(!1)}catch(a){}}})}else{d.current_y_offset=0,d.maxwave=g,d.handEffect="off";try{d.jittering_anim.kill(!1)}catch(a){}}},[e,a,b])});else{var k=c.eow*b.jitter_distance_horizontal,l=c.eow*b.jitter_offset_horizontal,m=c.eoh*b.jitter_distance,n=c.eoh*b.jitter_offset;if(b.rotatespeed=void 0!==b.hand_angle_repeat?parseFloat(j)/parseFloat(b.hand_angle_repeat):j>1?j/6:j>.5?j/6:j/3,b.jitterspeed=void 0!==b.jitter_repeat?parseFloat(j)/parseFloat(b.jitter_repeat):j>1?j/6:j>.5?j/6:j/3,"right_to_left"==b.hand_direction){var o=c.eow-l,p=o-k;punchgs.TweenLite.fromTo(e,j,{x:o},{x:p,ease:a.layersettings.frames[0].ease,onComplete:function(){b.handEffect="off"}})}else if("top_to_bottom"==b.hand_direction){var q=n,r=q+m;punchgs.TweenLite.fromTo(e,j,{y:q},{y:r,ease:a.layersettings.frames[0].ease,onComplete:function(){b.handEffect="off"}}),b.jitter_direction="horizontal"}else if("bottom_to_top"==b.hand_direction){var q=c.eoh-n,r=q-m;punchgs.TweenLite.fromTo(e,j,{y:q},{y:r,ease:a.layersettings.frames[0].ease,onComplete:function(){b.handEffect="off"}}),b.jitter_direction="horizontal"}else{var o=l,p=o+k;punchgs.TweenLite.fromTo(e,j,{x:o},{x:p,ease:a.layersettings.frames[0].ease,onComplete:function(){b.handEffect="off"}})}}g(e,a,b),f(e,a,b);break;case"move":d.data("outspeed",a.layersettings.frames[a.layersettings.frames.length-1].speed/1e3),d.data("outease",a.layersettings.frames[a.layersettings.frames.length-1].ease),i[0].eventCallback("onUpdate",function(a){var e=(this.target.style,this.target._gsTransform),f={};f.x=e.x,f.y=e.y,void 0===d.data("pos")&&d.data("pos",f),b.hand_inner_animation=punchgs.TweenLite.set(d,{x:f.x,y:f.y})},[a])}d.data("wb",b)},j=function(a,b,c){var d=b.layer.data(),e=jQuery(d._pw).find(".wb-thehand"),f=e.data("pos")||{x:0,y:0},g=e.position()||{top:0,left:0},h=d.frames[b.layersettings.frames.length-1].speed/1e3||2,i=d.frames[b.layersettings.frames.length-1].ease,j=0==f.x&&0==f.y?0:1;if(h=.5*h,"move"!=c.hand_function){var k=jQuery(d._pw).position();a.c.offset();g.left="right"==c.hand_type?a.c.width()-k.left+d.eow:0-k.left-d.eow,g.top=a.c.height()}punchgs.TweenLite.to(e,h,{top:g.top,left:g.left,autoAlpha:j,x:f.x,y:f.y,ease:i,onComplete:function(){e.remove(),c.handadded=!1}})}}(jQuery);