.rs-addon-strip .tp-bgimg,
.rs-addon-strip .tp-kbimg-wrap {
	
	display: none;
	
}

.rs-addon-strip-wrap {
	
	position: absolute;
	top: 0;
	left: 0;
	z-index: 99;
	
}

.rs-addon-strip-horizontal {
	
	width: auto; 
	height: 100%; 
	display: inline-block;
	white-space: nowrap; 
	
}

.rs-addon-strip-vertical {
	
	width: 100%; 
	height: auto;
	
}

.rs-addon-strip-wrap ,
.rs-addon-strip-img {
	
	-webkit-user-select: none;
	-moz-user-select: none
	-ms-user-select: none
	user-select: none;

	-webkit-touch-callout: none;
	touch-callout: none;
	
}

.rs-addon-strip-horizontal .rs-addon-strip-img {
	
	display: inline-block; 
	width: auto !important; 
	height: inherit; 
	vertical-align: top;
	
}

.rs-addon-strip-vertical .rs-addon-strip-img {
	
	width: 100% !important; 
	height: auto;
	
}
