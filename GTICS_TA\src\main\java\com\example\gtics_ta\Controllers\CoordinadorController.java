package com.example.gtics_ta.Controllers;

import com.example.gtics_ta.Entity.*;
import com.example.gtics_ta.Repository.*;
import com.example.gtics_ta.Services.ImageService;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/coordinador")
public class CoordinadorController {
    @Autowired
    UsuarioRepository usuarioRepository;
    @Autowired
    private TipoEspacioRepository tipoEspacioRepository;
    @Autowired
    private ComentariosRepository comentariosRepository;
    // TipoComentarioRepository ya no se usa - ahora usamos ENUM
    @Autowired
    private EspaciosDeportivosRepository espaciosDeportivosRepository;
    @Autowired
    private ImageService imageService;

    @GetMapping("/perfil")
    public String coordinadorPerfil(@ModelAttribute("usuario") Usuario usuario, HttpSession session, Model model) {
        Usuario usuarioSesion = (Usuario) session.getAttribute("usuario");
        if (usuarioSesion == null) {
            return "redirect:/login";
        }

        // Recargar usuario desde la base de datos para obtener datos más recientes
        Usuario usuarioActualizado = usuarioRepository.findById(usuarioSesion.getId()).orElse(usuarioSesion);

        model.addAttribute("usuario", usuarioActualizado);
        return "coordinador/perfil";
    }

    @PostMapping("/guardarperfil")
    public String guardarPerfil(@ModelAttribute("usuario") @Valid Usuario usuario, BindingResult bindingResult,
                               @RequestParam("archivo") MultipartFile file, HttpSession session, Model model) {
        if(bindingResult.hasErrors()) {
            return "coordinador/perfil";
        }

        if(file.isEmpty()) {
            model.addAttribute("msg", "Debe seleccionar una imagen");
            return "coordinador/perfil";
        }

        try {
            // Obtener usuario real de la sesión
            Usuario usuarioSesion = (Usuario) session.getAttribute("usuario");
            if (usuarioSesion == null) {
                model.addAttribute("msg", "Sesión expirada");
                return "coordinador/perfil";
            }

            // Usar el nuevo servicio de imágenes con S3
            imageService.uploadUserProfileImage(usuarioSesion, file);

            // Recargar usuario actualizado desde la base de datos
            Usuario usuarioActualizado = usuarioRepository.findById(usuarioSesion.getId()).orElse(usuarioSesion);

            // Actualizar usuario en sesión con los datos más recientes
            session.setAttribute("usuario", usuarioActualizado);

            model.addAttribute("msg", "Imagen de perfil actualizada exitosamente");
            return "redirect:/coordinador/perfil";
        } catch (Exception e) {
            model.addAttribute("msg", "Error al subir la imagen: " + e.getMessage());
            e.printStackTrace(); // Para ver el error en consola
            return "coordinador/perfil";
        }
    }

    @GetMapping("/profileimage/{id}")
    public ResponseEntity<byte[]> mostrarImagenPerfil(@PathVariable("id") Integer id) {
        Optional<Usuario> optusuario = usuarioRepository.findById(id);
        if(optusuario.isPresent()) {
            Usuario usuario = optusuario.get();

            // Si tiene URL de S3, redirigir
            if (usuario.getFotoUrl() != null && !usuario.getFotoUrl().isEmpty()) {
                return ResponseEntity.status(HttpStatus.FOUND)
                        .location(URI.create(usuario.getFotoUrl()))
                        .build();
            }

            // Fallback para imágenes BLOB (migración)
            byte[] image = usuario.getFoto();
            if (image == null) {
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
            }

            String tipoArchivo = usuario.getFotoTipoArchivo();
            if (tipoArchivo == null || tipoArchivo.isBlank()) {
                tipoArchivo = "application/octet-stream"; // tipo MIME por defecto
            }

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.parseMediaType(tipoArchivo));

            return new ResponseEntity<>(image, httpHeaders, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/principal")
    public String mostrarPaginaPrincipal(HttpSession  session, Model model) {
        // Obtener lista de tipos de espacio desde la BD
        List<TipoEspacio> tiposEspacio = tipoEspacioRepository.findAllByOrderByNombreAsc();
        model.addAttribute("tiposEspacio", tiposEspacio);

        // Obtener lista de espacios deportivos para el select
        List<EspaciosDeportivos> espaciosDeportivos = espaciosDeportivosRepository.findAll();
        model.addAttribute("espaciosDeportivos", espaciosDeportivos);

        return "coordinador/principal";
    }




    @PostMapping("/actualizar/{id}")
    public String actualizarPerfilUsuario(@PathVariable Integer id,
                                @RequestParam("celular") String celular,
                                @RequestParam("foto") MultipartFile foto) {

        Usuario usuario = usuarioRepository.findById(id).orElse(null);
        if (usuario != null) {
            usuario.setNumCelular(Integer.parseInt(celular));
            try {
                if (!foto.isEmpty()) {
                    usuario.setFoto(foto.getBytes());
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            usuarioRepository.save(usuario);
        }
        return "redirect:/coordinador/perfil/" + id;
    }

    @PostMapping("/guardar-observacion")
    public String guardarObservacion(@RequestParam("tipoServicio") Integer tipoServicioId,
                                     @RequestParam("tipoComentario") String tipoComentario,
                                     @RequestParam("comentarios") String contenido,
                                     HttpSession session,
                                     RedirectAttributes redirectAttributes) {

        try {
            // Obtener el usuario de la sesión
            Usuario usuario = (Usuario) session.getAttribute("usuario");
            if (usuario == null) {
                redirectAttributes.addFlashAttribute("error", "Sesión expirada. Por favor, inicie sesión nuevamente.");
                return "redirect:/login";
            }

            // Validar que se haya seleccionado un tipo de servicio
            if (tipoServicioId == null) {
                redirectAttributes.addFlashAttribute("error", "Debe seleccionar un tipo de servicio.");
                return "redirect:/coordinador/principal";
            }

            // Validar que el contenido no esté vacío
            if (contenido == null || contenido.trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Debe ingresar una descripción.");
                return "redirect:/coordinador/principal";
            }

            // Buscar el primer espacio deportivo del tipo seleccionado
            // (Puedes modificar esto para permitir seleccionar un espacio específico)
            List<EspaciosDeportivos> espacios = espaciosDeportivosRepository.findByTipoEspacio_Id(tipoServicioId);
            if (espacios.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No se encontraron espacios para el tipo de servicio seleccionado.");
                return "redirect:/coordinador/principal";
            }

            EspaciosDeportivos espacio = espacios.get(0); // Tomar el primer espacio del tipo

            // Determinar el tipo de comentario basado en la selección del radio button
            // Ahora usamos ENUM en lugar de la tabla TipoComentario
            Comentarios.TipoComentario tipoComentarioEnum;
            if ("reparacion".equals(tipoComentario)) {
                tipoComentarioEnum = Comentarios.TipoComentario.REPARACION;
            } else {
                tipoComentarioEnum = Comentarios.TipoComentario.COMENTARIO;
            }

            // Crear y guardar el comentario
            Comentarios comentario = new Comentarios();
            comentario.setEspacio(espacio);
            comentario.setUsuario(usuario);
            comentario.setTipoComentario(tipoComentarioEnum);
            comentario.setContenido(contenido.trim());

            comentariosRepository.save(comentario);

            // Mensaje de éxito
            String tipoMensaje = tipoComentarioEnum == Comentarios.TipoComentario.REPARACION ? "reporte de reparación" : "observación";
            redirectAttributes.addFlashAttribute("success",
                "Su " + tipoMensaje + " ha sido registrado exitosamente para el espacio: " + espacio.getNombre());

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error",
                "Ocurrió un error al guardar la observación: " + e.getMessage());
        }

        return "redirect:/coordinador/principal";
    }

    @GetMapping("/mis-observaciones")
    public String verMisObservaciones(HttpSession session, Model model) {
        Usuario usuario = (Usuario) session.getAttribute("usuario");
        if (usuario == null) {
            return "redirect:/login";
        }

        // Obtener todas las observaciones del coordinador
        List<Comentarios> misComentarios = comentariosRepository.findByUsuarioOrderByFechaCreacionDesc(usuario);
        model.addAttribute("comentarios", misComentarios);

        return "coordinador/mis-observaciones";
    }

}
