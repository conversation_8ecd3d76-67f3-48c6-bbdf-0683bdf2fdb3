(function(){function q(a,b){var c=[],m=a[0].opt.gridwidth,k=a[0].opt.visibilityLevels;Array.isArray(this.gridWidth)&&(m=m[0]);a.find("> ul > li").each(function(e){if(this.hasAttribute("data-filmstrip")){var d=l(this),n={transition:"fade",masterspeed:300};0===e&&(n.fstransition="fade",n.fsmasterspeed=300);c[c.length]=new r(a[0],b,m,k,d.data(n),JSON.parse(this.getAttribute("data-filmstrip")))}});this.slider=a;this.strips=c;a.one("revolution.slide.onloaded",this.onLoaded.bind(this)).on("revolution.slide.onbeforeswap",
this.beforeSwap.bind(this)).on("revolution.slide.onafterswap",this.afterSwap.bind(this))}function v(a){var b={};b[a.direction]=a.resetPosition;punchgs.TweenLite.set(a.strip,b);t.call(a)}function t(){var a={ease:punchgs.Linear.easeNone,onComplete:v,onCompleteParams:[this]};a[this.direction]=this.moveTo;this.carousel?this.tween?this.tween.resume():this.tween=punchgs.TweenLite.to(this.strip,this.time,a):punchgs.TweenLite.to(this.strip,this.time,a)}function u(a){var b=document.createElement("img");b.className=
"rs-addon-strip-img";b.setAttribute("data-lazyload",a.url);a.alt&&b.setAttribute("alt",a.alt);b.src=f+"/public/assets/images/transparent.png";return b}function r(a,b,c,m,k,e){for(var d,n=e.imgs,h=n.length,f=document.createDocumentFragment(),p="left-to-right"===e.direction||"top-to-bottom"===e.direction,g=0;2>g;g++)if(p)for(d=h;d--;)f.appendChild(u(n[d]));else for(d=0;d<h;d++)f.appendChild(u(n[d]));h=document.createElement("div");g=e.filter?" "+e.filter:"";d=-1!==e.direction.search(/left|right/)?"horizontal":
"vertical";h.className="rs-addon-strip-wrap rs-addon-strip-"+d+g;h.appendChild(f);k[0].appendChild(h);k[0].className+=" rs-addon-strip";this.strip=h;this.slide=k;this.slider=a;this.levels=m;this.reverse=p;this.carousel=b;this.gridWidth=c;this.times=e.times.split(",");this.resizer=this.sizer.bind(this);this.direction="horizontal"===d?"x":"y";a=e.times.split(",");h=a.length;speeds=[];for(g=0;4>g;g++)(b=g<h?parseInt(a[g]):10)?2>b&&(b=2):b=10,speeds[g]=b;this.times=speeds;l.data(k[0],"rs-addon-strip-"+
k[0].getAttribute("data-index"),this)}var l,p,f;window.RsFilmstripAddOn=function(a,b,c,m){a&&b&&(l=a,p=l(window),f=c,"/"===f.substr(f.length-1)&&(f=f.slice(0,-1)),l.event.special.rsStripDestroyed={remove:function(a){a.handler()}},new q(b,m))};q.prototype={onLoaded:function(){if(!this.checkRemoved())for(var a=this.strips.length,b=0;b<a;b++)this.strips[b].slide.find(".slotholder").append(this.strips[b].strip)},beforeSwap:function(a,b){this.checkRemoved()||b.nextslide.hasClass("rs-addon-strip")&&l.data(b.nextslide[0],
"rs-addon-strip-"+b.nextslide[0].getAttribute("data-index")).start()},afterSwap:function(a,b){if(!this.checkRemoved()&&b.prevslide.hasClass("rs-addon-strip"))l.data(b.prevslide[0],"rs-addon-strip-"+b.prevslide[0].getAttribute("data-index")).onStop()},checkRemoved:function(){return this.slider&&document.body.contains(this.slider[0])?!1:(this.destroy(),!0)},destroy:function(){p.off("resize.rsaddonstrip");if(this.strips)for(;this.strips.length;)this.strips[0].destroy(),this.strips.shift();for(var a in this)this.hasOwnProperty(a)&&
delete this[a]}};r.prototype={start:function(){clearTimeout(this.timer);this.resizeAdded||this.addResize();if(!this.carousel||this.carousel&&!this.tween){var a={};a[this.direction]=this.resetPosition;punchgs.TweenLite.set(this.strip,a)}this.running=!0;this.timer=setTimeout(this.onStart.bind(this),100)},onStart:function(){this.carousel||(this.strip.style.opacity="1");t.call(this)},stop:function(){clearTimeout(this.timer);this.carousel?this.tween?this.tween.pause():punchgs.TweenLite.killTweensOf(this.strip):
punchgs.TweenLite.killTweensOf(this.strip)},onStop:function(){this.running=!1;this.carousel||(this.strip.style.opacity="0");this.stop()},addResize:function(){p.on("resize.rsaddonstrip",this.onResize.bind(this));"x"===this.direction&&(this.strip.style.height=this.slider.clientHeight+"px");this.resizeAdded=!0;this.sizer(!0)},onResize:function(){clearTimeout(this.resize);this.carousel&&delete this.tween;this.stop();"x"===this.direction&&(this.strip.style.height=this.slider.clientHeight+"px");this.resize=
setTimeout(this.resizer,100)},sizer:function(){for(var a=this.slider.clientWidth,b,c=0;4>c;c++)if(a>=this.levels[c]){this.time=this.times[c];b=!0;break}b||(this.time=this.times[3]);"x"===this.direction?(b=this.strip.clientWidth,this.strip.style.height=this.slider.clientHeight+"px",this.reverse?(this.moveTo=-(b/2-a),this.resetPosition=-(b-a)):(this.moveTo=-(b/2),this.resetPosition=0)):(a=this.strip.clientHeight,this.reverse?(b=this.slider.clientHeight,this.moveTo=-(a/2-b),this.resetPosition=-(a-b)):
(this.moveTo=-(a/2),this.resetPosition=0));this.running?this.start():this.carousel&&(a={},a[this.direction]=this.resetPosition,punchgs.TweenLite.set(this.strip,a))},destroy:function(){clearTimeout(this.timer);clearTimeout(this.resize);punchgs.TweenLite.killTweensOf(this.strip);l.removeData(this.slide[0],"rs-addon-strip-"+this.slide[0].getAttribute("data-index"));for(var a in this)this.hasOwnProperty(a)&&delete this[a]}}})();