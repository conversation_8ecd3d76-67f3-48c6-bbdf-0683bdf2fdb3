/*
 2018 ThemePunch
*/
(function(){function l(a,b){this.slider=a;this.effects=b;this.firstrun=!0;a.one("revolution.slide.onloaded",this.onLoaded.bind(this)).one("rsLiquidEffectDestroyed",this.destroy.bind(this))}function k(a,b,c){this.w=b;this.h=c;this.slide=a;this.settings=a.data("liquideffectsettings");this.orig=a.data("liquideffectorig");this.displacement=new PIXI.Sprite.fromImage(this.settings.image,!0);this.displacement.texture.baseTexture.wrapMode=PIXI.WRAP_MODES.REPEAT;this.displacement.scale.x=2;this.displacement.scale.y=
2;this.displacement.anchor.set(.5);this.displacement.x=b/2;this.displacement.y=c/2;var g=new PIXI.Texture.fromImage(this.settings.sprite,!0),d=new PIXI.Container;this.img=new PIXI.Sprite(g);this.img.anchor.set(.5);this.img.x=b/2;this.img.y=c/2;d.addChild(this.img);this.filter=new PIXI.filters.DisplacementFilter(this.displacement);this.filter.autoFit=!0;this.stage=new PIXI.Container;this.stage.addChild(d);this.stage.addChild(this.displacement);this.stage.filters=[this.filter];this.stage.interactive=
!0;this.renderer=new PIXI.autoDetectRenderer(b,c,{transparent:!0});b=this.renderer.view.style;b.objectFit="cover";b.width="100%";b.height="100%";b.top="50%";b.left="50%";b.transform="translate( -50%, -50% ) scale(1.2)";a.find(".slotholder").append(this.renderer.view);this.settings.autoplay?(this.filter.scale.x=this.settings.scalex,this.filter.scale.y=this.settings.scaley):(this.filter.scale.x=0,this.filter.scale.y=0);this.settings.interactive?(d.interactive=!0,"mousedown"===this.settings.event?(d.buttonMode=
!0,d.pointerdown=this.onClick.bind(this),d.pointerup=d.pointerout=this.onReturn.bind(this)):(d.pointerover=this.onMouseEnter.bind(this),d.pointermove=this.onMouseMove.bind(this),d.pointerout=this.onMouseLeave.bind(this))):this.renderer.view.style.pointerEvents="none";this.supressEvents=!0;this.started=!1;this.ticker=new PIXI.ticker.Ticker;this.ticker.add(this.tick.bind(this))}var h,m="ontouchend"in document;window.RsLiquideffectAddOn=function(a,b){if(a&&b&&"undefined"!==typeof PIXI){h=a;h.event.special.rsLiquidEffectDestroyed=
{remove:function(a){a.handler()}};var c=b.find("li[data-liquideffect]").each(function(){var a=h(this),d=JSON.parse(a.attr("data-liquideffect"));if(d){var e=a.children("img");var c=b[0].opt.jsFileLocation||"";c&&(c=c.split("/revslider"));c=2===c.length?c[0]:"";/all|smart/.test(b[0].opt.lazyType)||(b[0].opt.lazyType="smart");if(e.attr("data-lazyload"))var f=e.attr("data-lazyload");else f=e.attr("src"),e.attr("data-lazyload",f),e.attr("src",c.replace("/extensions","")+"/revslider/admin/assets/images/dummy.png");
f||(f=a.find(".slotholder").contents().filter(function(){return 8===this.nodeType}),f.length?(e=f[0].nodeValue,f=!1,e&&(e=e.split('data-lazyload="'),2===e.length?(e=e[1].split('"'),1<e.length&&(f=e[0])):(e=e.split('src="'),2===e.length&&(e=e[1].split('"'),1<e.length&&(f=e[0]))))):f=!1);f?(d.sprite=f,m&&d.mobile&&(d.interactive=!1),f=d.easing.split("."),d.easing=punchgs[f[0]][f[1]],a.attr({"data-transition":d.transcross?"crossfade":"fade","data-masterspeed":d.transtime,"data-easein":d.easing,"data-easeout":d.easing}).removeAttr("data-kenburns").data({liquideffectsettings:d,
liquideffectorig:jQuery.extend({},d)}),d.interactive&&(f=d.intereasing.split("."),d.intereasing=punchgs[f[0]][f[1]])):a.removeData("liquideffect").removeAttr("data-liquideffect")}});c=b.find("li[data-liquideffect]");return c.length?new l(b,c):!1}};l.prototype={onLoaded:function(){this.slider.on("revolution.slide.onbeforeswap",this.beforeChange.bind(this)).on("revolution.slide.onchange",this.onChange.bind(this))},onChange:function(a,b){if(this.ranOnce){var c=b.prevslide.removeClass("liquid-force-visible").data("liquideffectcanvas");
c&&(c.ticker.stop(),c.tweenOut=null);(c=b.currentslide.data("liquideffectcanvas"))&&!c.started&&c.animateIn()}else this.beforeChange(!1,{nextslide:b.currentslide})},beforeChange:function(a,b){function c(a,c){var d=new Image,e=c.find(".tp-bgimg");d.crossOrigin="Anonymous";d.src=e.css("background-image").slice(4,-1).replace(/"/g,"");d.onload=function(){var e=new k(c,parseInt(d.naturalWidth,10),parseInt(d.naturalHeight,10));c.data("liquideffectcanvas",e);a.imgCount++;a.imgCount===g.length&&a.run(b)}}
this.ranOnce=!0;var g=[];this.effectsCreated||(this.effectsCreated=!0,this.effects.each(function(){var a=h(this),b=a.data("liquideffectsettings").imagesize.split("|");2===b.length?a.data("liquideffectcanvas",new k(a,parseInt(b[0],10),parseInt(b[1],10))):g[g.length]=this}));if(g.length)for(var d=this.imgCount=0;d<g.length;d++)c(this,h(g[d]));else this.run(b)},run:function(a){var b;if(this.firstrun)(b=a.nextslide.data("liquideffectcanvas"))&&b.animateIn(!0),this.firstrun=!1;else if(a.currentslide&&
(b=a.currentslide.data("liquideffectcanvas"))&&b.animateOut(a.nextslide),b=a.nextslide.data("liquideffectcanvas"))a.nextslide.addClass("liquid-force-visible"),b.settings.transcross&&b.animateIn()},destroy:function(){this.slider&&this.slider.off("revolution.slide.onloaded revolution.slide.onbeforeswap revolution.slide.onafterswap");this.effects&&this.effects.each(function(){jQuery(this).removeData("liquideffectcanvas liquideffectsettings liquideffectorig")});for(var a in this)this.hasOwnProperty(a)&&
delete this[a]}};k.prototype={tick:function(a){this.settings.autoplay&&(this.settings.speedx&&(this.displacement.x+=this.settings.speedx*a),this.settings.speedy&&(this.displacement.y+=this.settings.speedy),this.settings.rotationx&&(this.displacement.rotation.x+=this.settings.rotationx),this.settings.rotationy&&(this.displacement.rotation.y+=this.settings.rotationy),this.settings.rotation&&(this.displacement.rotation+=this.settings.rotation*Math.PI/180));this.renderer.render(this.stage)},onClick:function(){if(!this.supressEvents){var a=
.001*this.settings.intertime;if(this.settings.interscalex||this.settings.interscaley){var b={ease:this.settings.intereasing,overwrite:"all"};this.settings.interscalex&&(b.x=this.orig.scalex+this.settings.interscalex);this.settings.interscaley&&(b.y=this.orig.scaley+this.settings.interscaley);punchgs.TweenLite.to(this.filter.scale,a,b)}punchgs.TweenLite.to(this.settings,a,{speedx:this.orig.speedx+this.settings.interspeedx,speedy:this.orig.speedy+this.settings.interspeedy,rotation:this.orig.rotation+
this.settings.interotation,ease:this.settings.intereasing,overwrite:"all"})}},onReturn:function(){if(!this.supressEvents){var a=.001*this.settings.intertime;punchgs.TweenLite.to(this.filter.scale,a,{x:this.orig.scalex,y:this.orig.scaley,ease:this.settings.intereasing,overwrite:"all"});punchgs.TweenLite.to(this.settings,a,{speedx:this.orig.speedx,speedy:this.orig.speedy,rotation:this.orig.rotation,ease:this.settings.intereasing,overwrite:"all"})}},onMouseMove:function(a){if(!this.supressEvents)if(this.entered){var b=
a.data.global.x;a=a.data.global.y;var c=b-this.x,g=a-this.y,d=Date.now();g=Math.sqrt(c*c+g*g)/(d-this.t);c=.001*this.settings.intertime;this.x=b;this.y=a;this.t=d;if(this.settings.interscalex||this.settings.interscaley){b={ease:this.settings.intereasing,overwrite:"all",onComplete:this.onReturn.bind(this)};this.settings.interscalex&&(b.x=this.settings.interscalex*g);this.settings.interscaley&&(b.y=this.settings.interscaley*g);var e=!0;punchgs.TweenLite.to(this.filter.scale,c,b)}b={speedx:this.orig.speedx+
this.settings.interspeedx,speedy:this.orig.speedy+this.settings.interspeedy,rotation:this.orig.rotation+this.settings.interotation,ease:this.settings.intereasing,overwrite:"all"};e||(b.onComplete=this.onReturn.bind(this));punchgs.TweenLite.to(this.settings,c,b)}else this.onMouseEnter(a)},onMouseEnter:function(a){this.supressEvents||(this.entered=!0,this.x=a.data.global.x,this.y=a.data.global.y,this.t=Date.now())},onMouseLeave:function(){this.entered=!1},eventsReady:function(){this.supressEvents=!1},
onComplete:function(){var a=this.nextslide.data("liquideffectcanvas");a&&!a.started&&a.animateIn();this.nextslide=!1},onUpdateIn:function(){this.tweenIn&&(this.displacement.rotation+=.02*this.tweenIn.progress(),this.displacement.scale.set(3*this.tweenIn.progress()))},onUpdateOut:function(){this.tweenOut&&(this.displacement.rotation+=.02*this.tweenOut.progress(),this.displacement.scale.set(3*this.tweenOut.progress()))},transitionIn:function(){var a=.001*this.settings.transtime,b={x:this.orig.scalex,
y:this.orig.scaley,ease:this.settings.easing,overwrite:"all",delay:this.del},c={speedx:this.orig.speedx,speedy:this.orig.speedy,rotationx:this.orig.rotationx,rotationy:this.orig.rotationy,rotation:this.orig.rotation,ease:this.settings.easing,overwrite:"all",delay:this.del};this.interactive&&"mousedown"===this.event?b.onComplete=this.eventsReady.bind(this):this.supressEvents=!1;this.settings.transpower&&(c.onUpdate=this.onUpdateIn.bind(this));punchgs.TweenLite.to(this.filter.scale,a,b);this.tweenIn=
punchgs.TweenLite.to(this.settings,a,c);punchgs.TweenLite.to(this.renderer.view,.5*a,{opacity:1,ease:this.settings.easing,overwrite:"all",delay:this.del});this.ticker.start()},animateIn:function(a){this.reset();this.started=!0;this.del=this.settings.transcross||a?5E-4*this.settings.transtime:0;this.timer=setTimeout(this.transitionIn.bind(this),this.del)},animateOut:function(a){clearTimeout(this.timer);this.tweenIn=null;this.supressEvents=!0;this.started=!1;var b=.001*this.settings.transtime,c={speedx:this.orig.speedx+
this.settings.transpeedx,speedy:this.orig.speedy+this.settings.transpeedy,rotationx:this.orig.rotationx+this.settings.transrotx,rotationy:this.orig.rotationy+this.settings.transroty,rotation:this.orig.rotation+this.settings.transrot,ease:this.settings.easing,overwrite:"all"};this.settings.transcross&&a&&(this.nextslide=a,c.onComplete=this.onComplete.bind(this));this.settings.transpower&&(c.onUpdate=this.onUpdateOut.bind(this));punchgs.TweenLite.to(this.filter.scale,b,{x:this.orig.scalex+this.settings.transitionx,
y:this.orig.scaley+this.settings.transitiony,ease:this.settings.easing,overwrite:"all"});this.tweenOut=punchgs.TweenLite.to(this.settings,b,c);punchgs.TweenLite.to(this.renderer.view,b,{opacity:0,ease:this.settings.easing,delay:.5*b})},reset:function(a){this.tweenOut=this.tweenIn=null;this.ticker.stop();clearTimeout(this.timer);punchgs.TweenLite.killTweensOf(this.filter.scale);punchgs.TweenLite.killTweensOf(this.settings);punchgs.TweenLite.killTweensOf(this.renderer.view);a||(this.settings.power&&
(this.displacement.rotation=0,this.displacement.scale.set(1)),this.displacement.x=this.w/2,this.displacement.y=this.h/2,this.displacement.rotation.x=0,this.displacement.rotation.y=0,this.displacement.rotation=0,this.settings.speedx=this.orig.speedx+this.settings.transpeedx,this.settings.speedy=this.orig.speedy+this.settings.transpeedy,this.settings.rotationx=this.orig.rotationx+this.settings.transrotx,this.settings.rotationy=this.orig.rotationy+this.settings.transroty,this.filter.scale.x=this.orig.scalex+
this.settings.transitionx,this.filter.scale.y=this.orig.scaley+this.settings.transitiony,this.renderer.view.style.opacity=0)},destroy:function(){this.ticker&&(this.reset(!0),this.container.pointerdown=null,this.container.pointerup=null,this.container.pointerover=null,this.container.pointerout=null,this.container.touchstart=null,this.container.touchend=null);this.renderer&&this.slide.remove(this.renderer.view);for(var a in this)this.hasOwnProperty(a)&&delete this[a]}}})();