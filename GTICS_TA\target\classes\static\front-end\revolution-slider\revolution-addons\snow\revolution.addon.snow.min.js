/**
 * <AUTHOR> <<EMAIL>>
 * @link      http://www.themepunch.com/
 * @copyright 2016 ThemePunch
 * @version   1.0.0
 */

;var RsSnowAddOn=function(e,c){if(c){if(!c.children(".tp-static-layers").length){var f=document.createElement("div");f.className="tp-static-layers";f.style="pointer-events: none";c[0].appendChild(f)}var d=c[0].opt.snow,k=d.startSlide,g=d.endSlide,a,m,d={selector:".tp-static-layers",dimension:"self",particleMaxPer:parseInt(d.maxNum,10),particlaSize:[parseFloat(d.minSize),parseFloat(d.maxSize)],particleOpacity:[parseFloat(d.minOpacity),parseFloat(d.maxOpacity)],particleSpeed:[parseInt(d.minSpeed,10),
parseInt(d.maxSpeed,10)],particleSinus:[parseInt(d.min<PERSON>inus,10),parseInt(d.maxSinus,10)]};c.on("revolution.slide.onchange",function(e,b){var h=b.slideIndex;"first"===k&&(k=1);"last"===g&&(g=c.revmaxslide());h>=k&&h<=g?(m?a||c.letItSnow("winter"):(c.letItSnow(d),m=!0),a=!0):m&&(c.letItSnow("summer"),a=!1)})}};
(function(e,c){function f(a){a.pause=!0;a.sc.find(".snowflakes_wrapper").remove();a.c.removeData("snowflakes")}function d(a){a.snowflakes=[];for(var c=a.w*a.h/15E5;a.snowflakes.length<a.particleMaxPer*c;)a.snowflakes.push(g(a))}function k(a){window.requestAnimationFrame(function(){if(a!=c&&a.ctx!=c&&1!=a.destroyed&&1!=a.pause){a.ctx.clearRect(0,0,2700,2500);var d=a.h/3,e=a.h/3*2,b;for(b in a.snowflakes)if(!(0>a.snowflakes[b].y+.1*a.snowflakes[b].r&&1==a.summer||a.snowflakes[b].y>a.h+a.snowflakes[b].r&&
1==a.summer)){a.snowflakes[b].delta+=a.snowflakes[b].delta==Math.PI/2?-a.snowflakes[b].delta:Math.random()/500-.01;a.summer?a.snowflakes[b].y+=a.snowflakes[b].speed/50+.1*a.snowflakes[b].r:a.snowflakes[b].y+=a.snowflakes[b].speed/100+.1*a.snowflakes[b].r;a.snowflakes[b].x+=.1*Math.sin(a.snowflakes[b].delta)*a.snowflakes[b].r;a.snowflakes[b].y>a.h+a.snowflakes[b].r&&1!=a.summer&&(a.snowflakes[b]=g(a),a.snowflakes[b].y=0-a.snowflakes[b].r);var h=a.snowflakes[b].y-d,l=a.snowflakes[b].r,f=a.snowflakes[b].alpha;
if(0<h||1==a.summer)h=1-h/e,l=a.snowflakes[b].r*h,f=a.snowflakes[b].alpha*h;l=.1>l?.1:l;f=.1>f?.1:f;a.snowflakes[b].x=a.snowflakes[b].x>a.w+a.snowflakes[b].r?0:a.snowflakes[b].x<-l?a.w:a.snowflakes[b].x;a.ctx.beginPath();a.ctx.arc(a.snowflakes[b].x,a.snowflakes[b].y,l,2*Math.PI,!1);a.ctx.fillStyle="rgba(255,255,255,"+f+")";a.ctx.fill()}k(a)}})}function g(a){var c={};return c.delta=(a.particleSinus[0]+Math.random()*(a.particleSinus[1]-a.particleSinus[0]))*Math.round(2*Math.random()-1),c.r=a.particlaSize[0]+
Math.random()*(a.particlaSize[1]-a.particlaSize[0]),c.alpha=a.particleOpacity[0]+Math.random()*(a.particleOpacity[1]-a.particleOpacity[0]),c.speed=(a.particleSpeed[0]+Math.random()*(a.particleSpeed[1]-a.particleSpeed[0]))*c.r/3,c.x=Math.random()*a.w,c.y=Math.random()*-a.h,c}e.fn.extend({letItSnow:function(a){var g={particleMaxPer:400,particlaSize:[.2,6],particleOpacity:[.3,1],particleSpeed:[30,100],particleSinus:[1,100]};return"destroy"!=a&&"stop"!=a&&"play"!=a&&"summer"!=a&&"winter"!=a&&(a=e.extend(!0,
{},g,a)),this.each(function(){if(-1!=e.inArray(a,["destroy","stop","play","winter","summer"])){switch(a){case "destroy":a=e(this).data("snowflakes");a!=c&&f(a);break;case "stop":a=e(this).data("snowflakes");a!=c&&(a.pause=!0);break;case "play":a=e(this).data("snowflakes");a!=c&&(a.pause=!1,k(a));break;case "summer":a=e(this).data("snowflakes");a!=c&&(a.summer=!0);break;case "winter":a=e(this).data("snowflakes"),a!=c&&(a.summer=!1)}return!1}return a.c=e(this),a.sc=a.selector!=c?e(this).find(a.selector):
a.c,0==a.sc.length?!1:a.c.data("snowflakes")!=c?!1:(a.sc.find(".snowflakes_wrapper").remove(),a.sc.append('<div class="snowflakes_wrapper" style="position:relative;z-index:0"><div class="snowflakes_wrapper_inner" style="overflow:hidden;position:relative"><canvas width="2700" height="2500" style="position:relative;" class="snowflake_canvas"></canvas></div></div>'),a.sw=a.sc.find(".snowflakes_wrapper_inner"),a.sw.data("caller_container",a.c),a.canvas=a.sc.find(".snowflake_canvas"),a.dimension!=self?
a.sizer=a.c:a.sizer=a.sc,a.w=a.sizer.width(),a.h=a.sizer.height(),a.sc.find(".snowflakes_wrapper_inner").css({width:a.w,height:a.h}),a.canvas=a.canvas[0],a.snowflakes=[],a.ctx=a.canvas.getContext("2d"),d(a),k(a),a.c.data("snowflakes",a),void e(window).resize(function(){clearTimeout(a.timer);a.timer=setTimeout(function(){a.w=a.sizer.width();a.h=a.sizer.height();a.sc.find(".snowflakes_wrapper_inner").css({width:a.w,height:a.h});d(a)},50)}))})}})})(jQuery);