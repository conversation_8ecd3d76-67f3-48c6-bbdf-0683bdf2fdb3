<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="icon" th:href="@{/images/logo-solo.png}">

  <title>Página principal - Coordinador</title>

  <!-- Vendors Style-->
  <link rel="stylesheet" th:href="@{/css/vendors_css.css}" >

  <!-- Font Awesome -->
  <link rel="stylesheet" th:href="@{/assets/icons/font-awesome/css/font-awesome.min.css}">

  <!-- Style-->
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <link rel="stylesheet" th:href="@{/css/skin_color.css}">
  <style>

    #recuadro {
      background-color: rgba(183, 225, 233, 0.185);
      border-radius: 12px;
      padding: 25px 35px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
    }

    #fecha {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 15px;
      color: #7caad4;
      margin-bottom: -10px;
    }

    #hora {
      font-size: 45px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-weight:bold;
      color: #7caad4;
      margin-bottom: 5px;
    }
  </style>

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
  <div id="loader"></div>

  <header class="main-header">
    <div class="d-flex align-items-center logo-box justify-content-center">
      <!-- Logo -->
      <a th:href="@{/coordinador/principal}" class="logo">
        <!-- logo-->
        <div class="logo-mini w-150 text-center">
          <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
        </div>
      </a>
    </div>
    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top">
      <!-- Sidebar toggle button-->
      <div class="app-menu">
        <ul class="header-megamenu nav">
          <li class="btn-group nav-item">
            <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
              <i data-feather="align-left"></i>
            </a>
          </li>
        </ul>
      </div>

      <div class="navbar-custom-menu r-side">
        <ul class="nav navbar-nav">
          <!-- Notifications -->
          <li class="dropdown notifications-menu">
            <a href="#" class="waves-effect waves-light dropdown-toggle btn-info-light" data-bs-toggle="dropdown" title="Notifications">
              <i data-feather="bell"></i>
            </a>
            <ul class="dropdown-menu animated bounceIn">

              <li class="header">
                <div class="p-20">
                  <div class="flexbox">
                    <div>
                      <h4 class="mb-0 mt-0">Notifications</h4>
                    </div>
                    <div>
                      <a href="#" class="text-danger">Clear All</a>
                    </div>
                  </div>
                </div>
              </li>

              <li>
                <!-- inner menu: contains the actual data -->
                <ul class="menu sm-scrol">
                  <li>
                    <a href="#">
                      <i class="fa fa-users text-info"></i> Curabitur id eros quis nunc suscipit blandit.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-warning text-warning"></i> Duis malesuada justo eu sapien elementum, in semper diam posuere.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-users text-danger"></i> Donec at nisi sit amet tortor commodo porttitor pretium a erat.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-shopping-cart text-success"></i> In gravida mauris et nisi
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-danger"></i> Praesent eu lacus in libero dictum fermentum.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-primary"></i> Nunc fringilla lorem
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-success"></i> Nullam euismod dolor ut quam interdum, at scelerisque ipsum imperdiet.
                    </a>
                  </li>
                </ul>
              </li>
              <li class="footer">
                <a href="#">View all</a>
              </li>
            </ul>
          </li>


          <!-- User Account-->
          <li>
            <a class="waves-effect waves-light dropdown-toggle w-auto l-h-12 bg-transparent py-0 no-shadow" data-bs-toggle="dropdown" title="User">
              <div class="d-flex pt-5">
                <div class="text-end me-10">
                  <p class="pt-5 fs-14 mb-0 fw-700 text-primary" th:text="${session.usuario.nombres+ ' ' + session.usuario.apellidos}" ></p>
                  <small class="fs-10 mb-0 text-uppercase text-mute" th:text="${session.usuario.rol.nombre}"></small>
                </div>
                <img th:src="@{|/coordinador/profileimage/${session.usuario.getId()}?t=${#dates.format(#dates.createNow(), 'yyyyMMddHHmmss')}|}" class="avatar rounded-10 bg-primary-light h-40 w-40" alt=""
                     onerror="this.onerror=null;this.src='/images/avatar/avatar-1.png';" />
              </div>
            </a>
          </li>

        </ul>
      </div>
    </nav>
  </header>

  <!-- Left side column. contains the logo and sidebar -->
  <aside class="main-sidebar">
    <!-- sidebar-->
    <section class="sidebar position-relative">
      <div class="multinav">
        <div class="multinav-scroll" style="height: 100%;">
          <!-- sidebar menu-->
          <ul class="sidebar-menu" data-widget="tree">
            <li>
              <a th:href="@{/coordinador/perfil(id=3)}">
                <i data-feather="user"></i>
                <span>Perfil</span>
              </a>
            </li>
            <li>
              <a th:href="@{/coordinador/principal}">
                <i data-feather="clock"></i>
                <span>Marcar asistencia</span>
              </a>
            </li>
            <li>
              <a th:href="@{/coordinador/mis-observaciones}">
                <i data-feather="file-text"></i>
                <span>Mis Observaciones</span>
              </a>
            </li>
            <!--Implementación proxima de horarios
            <li>
              <a href="horarios.html">
                <i data-feather="calendar"></i>
                <span>Horarios</span>
              </a>
            </li>-->

            <li>
              <a href="#" class="nav-link" onclick="document.getElementById('logoutForm').submit(); return false;">
                <i data-feather="log-out"></i>
                <span>Cerrar sesión</span>
              </a>
              <form id="logoutForm" th:action="@{/logout}" method="post" style="display: none;"></form>
            </li>

          </ul>

          <div class="sidebar-widgets">
            <div class="copyright text-center m-25">
              <p><strong class="d-block">Municipalidad de San Miguel</strong> © <script>document.write(new Date().getFullYear())</script> All Rights Reserved</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <div class="container-full">
      <!-- Content Header (Page header) -->
      <div class="content-header">
        <div class="d-flex align-items-center">
          <div class="me-auto">
            <h4 class="page-title">Perfil</h4>
            <div class="d-inline-block align-items-center">
              <nav>
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="#"><i class="mdi mdi-home-outline"></i></a></li>
                  <li class="breadcrumb-item" aria-current="page">Menú</li>
                </ol>
              </nav>
            </div>
          </div>

        </div>
      </div>

      <!-- Main content -->
      <section class="content">
        <div class="row">
          <div class="col-lg-5 col-12">
            <div class="box" style="background-color: rgba(136, 217, 231, 0.5);">
              <!-- /.box-header -->

              <div id="recuadro">
                <div id="fecha">Cargando fecha...</div>
                <div id="hora">00:00:00</div>
                <div id="frase">Marque su hora de entrada al complejo</div>
                <div style="text-align: center;padding: 10px;">
                  <button type="button" class="btn btn-warning me-1" id="btnEntrada" disabled>
                    Entrada
                  </button>
                  <button type="button" class="btn btn-primary" id="btnSalida" disabled>
                    Salida
                  </button>
                </div>
              </div>



            </div>
            <!-- /.box -->
          </div>

          <div class="col-lg-7 col-12">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">Observaciones</h4>
              </div>
              <!-- /.box-header -->
              <form class="form" th:action="@{/coordinador/guardar-observacion}" method="post">
                <div class="box-body">
                  <!-- Mostrar mensajes de éxito o error -->
                  <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i>
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>

                  <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>

                  <h4 class="box-title text-info mb-0"><i class="ti-map-alt me-15"></i>Establecimiento</h4>
                  <hr class="my-15">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Nombre del local</label>
                        <input type="text" class="form-control" disabled placeholder="Diego Ferre">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Tipo de servicio<span class="text-danger">*</span></label>
                        <select id="tipo_servicio" name="tipoServicio" required class="form-select" >
                          <option value="" disabled selected>Seleccione una opción</option>
                          <option th:each="tipo : ${tiposEspacio}"
                                  th:value="${tipo.id}"
                                  th:text="${tipo.nombre}">
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <h4 class="box-title text-info mb-0 mt-20"><i class="ti-comment-alt me-10"></i> Comentarios</h4>
                  <hr class="my-15">
                  <div class="row">
                    <div class="radio; col-md-6">
                      <input name="tipoComentario" type="radio" id="Option_1" value="reparacion" checked>
                      <label for="Option_1">🔧 Reparación/Mantenimiento</label>
                    </div>
                    <div class="radio; col-md-6">
                      <input name="tipoComentario" type="radio" id="Option_2" value="observacion">
                      <label for="Option_2">📝 Observación General</label>
                    </div>
                  </div>
                  <div class="form-group" style="margin-top: 15px;">
                    <label class="form-label">Descripción<span class="text-danger">*</span></label>
                    <textarea rows="4" class="form-control" name="comentarios" id="comentarios" required
                              placeholder="Explique brevemente la situación por la cual el establecimiento requiere arreglos o alguna observación sobre este."></textarea>
                  </div>
                </div>
                <!-- /.box-body -->
                <div class="box-footer text-center">
                  <button type="submit" class="btn btn-primary">
                    <i class="fa fa-paper-plane me-2"></i>Enviar Observación
                  </button>
                  <a href="/coordinador/mis-observaciones" class="btn btn-outline-info ms-2">
                    <i class="fa fa-list me-2"></i>Ver Mis Observaciones
                  </a>
                </div>
              </form>
            </div>
            <!-- /.box -->
          </div>

        </div>


      </section>
      <!-- /.content -->
    </div>
  </div>
  <!-- /.content-wrapper -->

  <footer class="main-footer">
    &copy; <script>document.write(new Date().getFullYear())</script> <a>New Fibra</a>. All Rights Reserved.
  </footer>
  <!-- Control Sidebar -->
  <aside class="control-sidebar">

    <div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
    <ul class="nav nav-tabs control-sidebar-tabs">
      <li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
      <li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
    </ul>
  </aside>
  <!-- /.control-sidebar -->
  <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
  <div class="control-sidebar-bg"></div>
</div>

<!--Script del contador de horas!-->
<!-- ./wrapper -->
<script>
  // Variables globales
  let inicioTrabajo = null;
  let intervaloContador = null;
  let intervaloReloj = null;
  let tiempoTranscurrido = 0;

  // Configuración de ubicaciones (actualiza estas coordenadas)
  const lugaresPermitidos = [
    {nombre: "Complejo Diego Ferré", lat: -12.0775, lng: -77.0933},
    {nombre: "Universidad Católica", lat: -12.069722, lng: -77.079722},
    {nombre: "Monte de los Olivos", lat: -12.134648, lng: -76.986915}
  ];
  const RADIO_PERMITIDO = 5000; // 5km (ajustar según necesidad)

  // Función mejorada de geolocalización
  async function obtenerUbicacion() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject("Tu navegador no soporta geolocalización");
        return;
      }

      const opciones = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      };

      navigator.geolocation.getCurrentPosition(
              position => resolve(position.coords),
              error => {
                switch(error.code) {
                  case error.PERMISSION_DENIED:
                    reject("Debes permitir el acceso a tu ubicación para usar esta función");
                    break;
                  case error.POSITION_UNAVAILABLE:
                    reject("No se pudo obtener tu ubicación");
                    break;
                  case error.TIMEOUT:
                    reject("Tiempo de espera agotado al obtener ubicación");
                    break;
                  default:
                    reject("Error desconocido al obtener ubicación");
                }
              },
              opciones
      );
    });
  }

  // Función mejorada de validación
  async function validarUbicacion() {
    try {
      const coords = await obtenerUbicacion();
      console.log('Coordenadas obtenidas:', coords.latitude, coords.longitude);

      const distancias = lugaresPermitidos.map(lugar => {
        const distancia = calcularDistancia(
                coords.latitude,
                coords.longitude,
                lugar.lat,
                lugar.lng
        );
        console.log(`Distancia a ${lugar.nombre}: ${Math.round(distancia)}m`);
        return distancia;
      });

      const valido = distancias.some(d => d <= RADIO_PERMITIDO);
      if (!valido) {
        const minima = Math.min(...distancias);
        throw new Error(`Fuera del área permitida (${Math.round(minima)}m de distancia)`);
      }

      // Habilitar botones si es válido
      document.getElementById('btnEntrada').disabled = false;
      document.getElementById('btnSalida').disabled = false;
      return true;

    } catch (error) {
      console.error('Error:', error);
      alert(error.message);
      // Deshabilitar botones en caso de error
      document.getElementById('btnEntrada').disabled = true;
      document.getElementById('btnSalida').disabled = true;
      return false;
    }
  }

  // Resto del código sin cambios...
  // [Mantener funciones calcularDistancia, formatearTiempo, actualizarReloj, registrarAccion]

  // Inicialización mejorada
  document.addEventListener('DOMContentLoaded', () => {
    // Configurar eventos
    document.getElementById('btnEntrada').addEventListener('click', () => registrarAccion('entrada'));
    document.getElementById('btnSalida').addEventListener('click', () => registrarAccion('salida'));

    // Iniciar reloj
    intervaloReloj = setInterval(actualizarReloj, 1000);
    actualizarReloj();

    // Validación inicial
    validarUbicacion().then(valido => {
      if(valido) {
        // Programar validación periódica
        setInterval(validarUbicacion, 120000);
      }
    });

    // Forzar actualización de permisos
    if(navigator.permissions) {
      navigator.permissions.query({name:'geolocation'})
              .then(permiso => {
                permiso.onchange = () => {
                  console.log('Estado del permiso cambiado:', permiso.state);
                  validarUbicacion();
                }
              });
    }
  });

  // Añade estas funciones al script
  function calcularDistancia(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Radio de la Tierra en metros
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) ** 2 + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ/2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  function formatearTiempo(segundos) {
    const horas = Math.floor(segundos / 3600);
    const minutos = Math.floor((segundos % 3600) / 60);
    const segs = segundos % 60;
    return `${String(horas).padStart(2, '0')}:${String(minutos).padStart(2, '0')}:${String(segs).padStart(2, '0')}`;
  }

  function actualizarReloj() {
    const fecha = new Date();
    const opcionesFecha = {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    };
    const opcionesHora = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };

    // Actualizar fecha
    document.getElementById('fecha').textContent = fecha.toLocaleDateString('es-PE', opcionesFecha);

    // Actualizar hora solo si no está en modo cronómetro
    if (!inicioTrabajo) {
      document.getElementById('hora').textContent = fecha.toLocaleTimeString('es-PE', opcionesHora);
    }
  }

  async function registrarAccion(tipo) {
    try {
      const ubicacionValida = await validarUbicacion();
      if (!ubicacionValida) return;

      const ahora = new Date();

      if (tipo === 'entrada') {
        inicioTrabajo = ahora;
        tiempoTranscurrido = 0;

        // Iniciar cronómetro
        intervaloContador = setInterval(() => {
          tiempoTranscurrido++;
          document.getElementById('hora').textContent = formatearTiempo(tiempoTranscurrido);
        }, 1000);

        // Actualizar UI
        document.getElementById('frase').textContent = "Tiempo trabajado:";
        document.getElementById('btnEntrada').disabled = true;
        document.getElementById('btnSalida').disabled = false;

      } else if (tipo === 'salida') {
        // Detener cronómetro
        clearInterval(intervaloContador);
        inicioTrabajo = null;

        // Restaurar reloj normal
        document.getElementById('frase').textContent = "Marque su hora de entrada";
        document.getElementById('btnEntrada').disabled = false;
        document.getElementById('btnSalida').disabled = true;
        actualizarReloj();
      }

      console.log(`${tipo.toUpperCase()} registrada a las ${ahora.toLocaleTimeString()}`);

    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  // Script para mejorar la experiencia del formulario de observaciones
  document.addEventListener('DOMContentLoaded', function() {
    const tipoServicioSelect = document.getElementById('tipo_servicio');
    const comentariosTextarea = document.getElementById('comentarios');
    const radioButtons = document.querySelectorAll('input[name="tipoComentario"]');

    // Actualizar placeholder según el tipo de comentario seleccionado
    function actualizarPlaceholder() {
      const tipoSeleccionado = document.querySelector('input[name="tipoComentario"]:checked').value;
      if (tipoSeleccionado === 'reparacion') {
        comentariosTextarea.placeholder = 'Describa detalladamente el problema o reparación necesaria. Incluya ubicación específica, urgencia y cualquier detalle relevante para el mantenimiento.';
      } else {
        comentariosTextarea.placeholder = '📝 Comparta su observación general sobre el espacio deportivo. Puede incluir sugerencias de mejora, comentarios sobre el estado actual o cualquier aspecto relevante.';
      }
    }

    // Agregar event listeners a los radio buttons
    radioButtons.forEach(radio => {
      radio.addEventListener('change', actualizarPlaceholder);
    });

    // Validación del formulario
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
      const tipoServicio = tipoServicioSelect.value;
      const comentarios = comentariosTextarea.value.trim();

      if (!tipoServicio) {
        e.preventDefault();
        alert('⚠️ Por favor, seleccione un tipo de servicio.');
        tipoServicioSelect.focus();
        return;
      }

      if (!comentarios) {
        e.preventDefault();
        alert('⚠️ Por favor, ingrese una descripción.');
        comentariosTextarea.focus();
        return;
      }

      if (comentarios.length < 10) {
        e.preventDefault();
        alert('⚠️ La descripción debe tener al menos 10 caracteres.');
        comentariosTextarea.focus();
        return;
      }

      // Confirmación antes de enviar
      const tipoComentario = document.querySelector('input[name="tipoComentario"]:checked').value;
      const tipoTexto = tipoComentario === 'reparacion' ? 'reporte de mantenimiento' : 'observación';
      const tipoServicioTexto = tipoServicioSelect.options[tipoServicioSelect.selectedIndex].text;

      const confirmacion = confirm(
        `¿Está seguro de enviar este ${tipoTexto} para el tipo de servicio "${tipoServicioTexto}"?\n\n` +
        `Descripción: ${comentarios.substring(0, 100)}${comentarios.length > 100 ? '...' : ''}`
      );

      if (!confirmacion) {
        e.preventDefault();
      }
    });

    // Inicializar placeholder
    actualizarPlaceholder();

    // Auto-dismiss alerts después de 5 segundos
    setTimeout(function() {
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(alert => {
        if (alert.querySelector('.btn-close')) {
          alert.querySelector('.btn-close').click();
        }
      });
    }, 5000);
  });

</script>


<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/d3.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/c3.min.js}"></script>

<!-- Rhythm Admin App -->
<script th:src="@{/js/template.js}"></script>
<script th:src="@{/js/pages/c3-data.js}"></script>


</body>
</html>
