/**
 * All of the CSS for your public-facing functionality should be
 * included in this file.
 */

 @font-face {
	 font-family: 'weather';
	 src: url('fonts/artill_clean_icons-webfont.eot');
	 src: url('fonts/artill_clean_icons-webfont.eot?#iefix') format('embedded-opentype'),
	 url('fonts/artill_clean_icons-webfont.woff') format('woff'),
	 url('fonts/artill_clean_icons-webfont.ttf') format('truetype'),
	 url('fonts/artill_clean_icons-webfont.svg#artill_clean_weather_iconsRg') format('svg');
	 font-weight: normal;
	 font-style: normal;
}

.revslider-weather-icon {
	font-family: 'weather';	
	text-align: inherit!important;
    line-height: inherit!important;
    border-width: inherit!important;
    margin: inherit!important;
    padding: inherit!important;
    letter-spacing: inherit!important;
    font-weight: inherit!important;
    font-size: inherit!important;
    font-style: inherit;
}

.revslider-weather-icon-0:before { content: ":"; }
.revslider-weather-icon-1:before { content: "p"; }
.revslider-weather-icon-2:before { content: "S"; }
.revslider-weather-icon-3:before { content: "Q"; }
.revslider-weather-icon-4:before { content: "S"; }
.revslider-weather-icon-5:before { content: "W"; }
.revslider-weather-icon-6:before { content: "W"; }
.revslider-weather-icon-7:before { content: "W"; }
.revslider-weather-icon-8:before { content: "W"; }
.revslider-weather-icon-9:before { content: "I"; }
.revslider-weather-icon-10:before { content: "W"; }
.revslider-weather-icon-11:before { content: "I"; }
.revslider-weather-icon-12:before { content: "I"; }
.revslider-weather-icon-13:before { content: "I"; }
.revslider-weather-icon-14:before { content: "I"; }
.revslider-weather-icon-15:before { content: "W"; }
.revslider-weather-icon-16:before { content: "I"; }
.revslider-weather-icon-17:before { content: "W"; }
.revslider-weather-icon-18:before { content: "U"; }
.revslider-weather-icon-19:before { content: "Z"; }
.revslider-weather-icon-20:before { content: "Z"; }
.revslider-weather-icon-21:before { content: "Z"; }
.revslider-weather-icon-22:before { content: "Z"; }
.revslider-weather-icon-23:before { content: "Z"; }
.revslider-weather-icon-24:before { content: "E"; }
.revslider-weather-icon-25:before { content: "E"; }
.revslider-weather-icon-26:before { content: "3"; }
.revslider-weather-icon-27:before { content: "a"; }
.revslider-weather-icon-28:before { content: "A"; }
.revslider-weather-icon-29:before { content: "a"; }
.revslider-weather-icon-30:before { content: "A"; }
.revslider-weather-icon-31:before { content: "6"; }
.revslider-weather-icon-32:before { content: "1"; }
.revslider-weather-icon-33:before { content: "6"; }
.revslider-weather-icon-34:before { content: "1"; }
.revslider-weather-icon-35:before { content: "W"; }
.revslider-weather-icon-36:before { content: "1"; }
.revslider-weather-icon-37:before { content: "S"; }
.revslider-weather-icon-38:before { content: "S"; }
.revslider-weather-icon-39:before { content: "S"; }
.revslider-weather-icon-40:before { content: "M"; }
.revslider-weather-icon-41:before { content: "W"; }
.revslider-weather-icon-42:before { content: "I"; }
.revslider-weather-icon-43:before { content: "W"; }
.revslider-weather-icon-44:before { content: "a"; }
.revslider-weather-icon-45:before { content: "S"; }
.revslider-weather-icon-46:before { content: "U"; }
.revslider-weather-icon-47:before { content: "S"; }