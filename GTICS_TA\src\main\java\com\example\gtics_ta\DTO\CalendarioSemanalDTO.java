package com.example.gtics_ta.DTO;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CalendarioSemanalDTO {
    
    private LocalDate fechaInicioSemana;
    private LocalDate fechaFinSemana;
    private List<DiaCalendario> dias;
    private Map<Integer, CoordinadorInfo> coordinadores;
    
    @Getter
    @Setter
    public static class DiaCalendario {
        private LocalDate fecha;
        private String nombreDia;
        private List<AsignacionDia> asignaciones;
        private List<ReservaDia> reservas;
        
        public DiaCalendario(LocalDate fecha, String nombreDia) {
            this.fecha = fecha;
            this.nombreDia = nombreDia;
        }
    }
    
    @Getter
    @Setter
    public static class AsignacionDia {
        private Integer coordinadorId;
        private String coordinadorNombre;
        private Integer espacioId;
        private String espacioNombre;
        private String espacioUbicacion;
        private LocalTime horaInicio;
        private LocalTime horaFin;
        private String observaciones;
        private boolean tieneConflicto;
        
        public AsignacionDia(Integer coordinadorId, String coordinadorNombre, 
                           Integer espacioId, String espacioNombre, String espacioUbicacion,
                           LocalTime horaInicio, LocalTime horaFin) {
            this.coordinadorId = coordinadorId;
            this.coordinadorNombre = coordinadorNombre;
            this.espacioId = espacioId;
            this.espacioNombre = espacioNombre;
            this.espacioUbicacion = espacioUbicacion;
            this.horaInicio = horaInicio;
            this.horaFin = horaFin;
            this.tieneConflicto = false;
        }
    }
    
    @Getter
    @Setter
    public static class ReservaDia {
        private Integer reservaId;
        private String usuarioNombre;
        private String espacioNombre;
        private LocalTime horaInicio;
        private LocalTime horaFin;
        private boolean tieneCoordinador;
        private String estadoReserva;
        
        public ReservaDia(Integer reservaId, String usuarioNombre, String espacioNombre,
                         LocalTime horaInicio, LocalTime horaFin, String estadoReserva) {
            this.reservaId = reservaId;
            this.usuarioNombre = usuarioNombre;
            this.espacioNombre = espacioNombre;
            this.horaInicio = horaInicio;
            this.horaFin = horaFin;
            this.estadoReserva = estadoReserva;
            this.tieneCoordinador = false;
        }
    }
    
    @Getter
    @Setter
    public static class CoordinadorInfo {
        private Integer id;
        private String nombre;
        private String apellidos;
        private String correo;
        private int totalHorasAsignadas;
        private int totalEspaciosAsignados;
        
        public CoordinadorInfo(Integer id, String nombre, String apellidos, String correo) {
            this.id = id;
            this.nombre = nombre;
            this.apellidos = apellidos;
            this.correo = correo;
            this.totalHorasAsignadas = 0;
            this.totalEspaciosAsignados = 0;
        }
        
        public String getNombreCompleto() {
            return nombre + " " + apellidos;
        }
    }
}
