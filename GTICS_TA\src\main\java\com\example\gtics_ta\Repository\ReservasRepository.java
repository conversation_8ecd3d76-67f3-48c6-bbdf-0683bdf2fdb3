package com.example.gtics_ta.Repository;

import com.example.gtics_ta.Entity.Reservas;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ReservasRepository extends JpaRepository<Reservas, Integer> {
    List<Reservas> findByEspacioDeportivo_NombreContainingIgnoreCase(String nombre);

    // Filtrar por tipo de espacio
    List<Reservas> findByEspacioDeportivo_TipoEspacio_Id(Integer tipoEspacioId);

    // Filtrar por tipo de espacio y nombre
    List<Reservas> findByEspacioDeportivo_TipoEspacio_IdAndEspacioDeportivo_NombreContainingIgnoreCase(Integer tipoEspacioId, String nombre);

    // Buscar reservas por ID de pago
    List<Reservas> findByPago_Id(Integer pagoId);

    // Buscar reservas por fecha
    List<Reservas> findByFechaReserva(LocalDate fechaReserva);

    // Buscar reservas en un rango de fechas
    List<Reservas> findByFechaReservaBetween(LocalDate fechaInicio, LocalDate fechaFin);

    // Buscar reservas activas (no canceladas)
    List<Reservas> findByEstadoReserva(Reservas.EstadoReserva estadoReserva);

    // Buscar reservas activas en un rango de fechas
    @Query("SELECT r FROM Reservas r WHERE r.fechaReserva BETWEEN :fechaInicio AND :fechaFin AND r.estadoReserva = 'ACTIVA' ORDER BY r.fechaReserva, r.horario.horaInicio")
    List<Reservas> findReservasActivasEnRango(@Param("fechaInicio") LocalDate fechaInicio, @Param("fechaFin") LocalDate fechaFin);

    // Buscar reservas sin coordinador asignado
    @Query("SELECT r FROM Reservas r WHERE r.estadoReserva = 'ACTIVA' AND r.fechaReserva >= CURRENT_DATE")
    List<Reservas> findReservasSinCoordinador();
}
