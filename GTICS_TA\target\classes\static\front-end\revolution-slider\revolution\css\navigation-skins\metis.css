/*-----------------------------------------------------------------------------

- Revolution Slider 5.0 Navigatin Skin Style  -

 METIS SKIN

author:  ThemePunch
email:      <EMAIL>
website:    http://www.themepunch.com
-----------------------------------------------------------------------------*/
/* ARROWS */
.metis.tparrows {
  background:#fff;
  padding:10px;
  transition:all 0.3s;
  -webkit-transition:all 0.3s;
  width:60px;
  height:60px;
  box-sizing:border-box;
 }
 
 .metis.tparrows:hover {
   background:#fff;
   background:rgba(255,255,255,0.75);
 }
 
 .metis.tparrows:before {
  color:#000;  
   transition:all 0.3s;
  -webkit-transition:all 0.3s;
 }
 
 .metis.tparrows:hover:before {
   transform:scale(1.5);
  }
 

/* BULLETS */
.metis .tp-bullet { 
    opacity:1;
    width:50px;
    height:50px;    
    padding:3px;
    background:#000;
    background-color:rgba(0,0,0,0.25);
    margin:0px;
    box-sizing:border-box;
    transition:all 0.3s;
    -webkit-transition:all 0.3s;
    border-radius:50%;
  }

.metis .tp-bullet-image {

   border-radius:50%;
   display:block;
   box-sizing:border-box;
   position:relative;
    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);
  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);
  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);
  width:44px;
  height:44px;
  background-size:cover;
  background-position:center center;
 }  
.metis .tp-bullet-title { 
     position:absolute; 
	 bottom:65px;
     display:inline-block;
     left:50%;
     background:#000;
     background:rgba(0,0,0,0.75);
     color:#fff;
     padding:10px 30px;
     border-radius:4px;
	 -webkit-border-radius:4px;
     opacity:0;
      transition:all 0.3s;
    -webkit-transition:all 0.3s;
    transform: translateZ(0.001px) translateX(-50%) translateY(14px);
    transform-origin:50% 100%;
    -webkit-transform: translateZ(0.001px) translateX(-50%) translateY(14px);
    -webkit-transform-origin:50% 100%;
    opacity:0;
    white-space:nowrap;
 }

.metis .tp-bullet:hover .tp-bullet-title {
  	 transform:rotateX(0deg) translateX(-50%);
    -webkit-transform:rotateX(0deg) translateX(-50%);
    opacity:1;
}

.metis .tp-bullet.selected,
.metis .tp-bullet:hover  {
  
   background: rgba(255,255,255,1);
  background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(119,119,119,1)));
  background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: -o-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#777777", GradientType=0 );
 
      }
.metis .tp-bullet-title:after {
        content:" ";
        position:absolute;
        left:50%;
        margin-left:-8px;
        width: 0;
		height: 0;
		border-style: solid;
		border-width: 8px 8px 0 8px;
		border-color: rgba(0,0,0,0.75) transparent transparent transparent;
        bottom:-8px;
   }

/* METIS TAB */
.metis .tp-tab-number {
        color: #fff;
        font-size: 40px;
        line-height: 30px;
        font-weight: 400;
        font-family: "Playfair Display";
        width: 50px;
        margin-right: 17px;
        display: inline-block;
        float: left;
    }
    .metis .tp-tab-mask {
        padding-left: 20px;
        left: 0px;
        max-width: 90px !important;
        transition: 0.4s padding-left, 0.4s left, 0.4s max-width;
    }
    .metis:hover .tp-tab-mask {
        padding-left: 0px;
        left: 50px;
        max-width: 500px !important;
    }
    .metis .tp-tab-divider {
        border-right: 1px solid transparent;
        height: 30px;
        width: 1px;
        margin-top: 5px;
        display: inline-block;
        float: left;
    }
    .metis .tp-tab-title {
        color: #fff;
        font-size: 20px;
        line-height: 20px;
        font-weight: 400;
        font-family: "Playfair Display";
        position: relative;
        padding-top: 10px;
        padding-left: 30px;
        display: inline-block;
        transform: translateX(-100%);
        transition: 0.4s all;
    }
    .metis .tp-tab-title-mask {
        position: absolute;
        overflow: hidden;
        left: 67px;
    }
    .metis:hover .tp-tab-title {
        transform: translateX(0);
    }
    .metis .tp-tab {
        opacity: 0.15;
        transition: 0.4s all;
    }
    .metis .tp-tab:hover,
    .metis .tp-tab.selected {
        opacity: 1;
    }
    .metis .tp-tab.selected .tp-tab-divider {
        border-right: 1px solid #cdb083;
    }
    .metis.tp-tabs {
        max-width: 118px !important;
        padding-left: 50px;
    }
    .metis.tp-tabs:before {
        content: " ";
        height: 100%;
        width: 88px;
        background: rgba(0, 0, 0, 0.15);
        border-right: 1px solid rgba(255, 255, 255, 0.10);
        left: 0px;
        top: 0px;
        position: absolute;
        transition: 0.4s all;
    }
    .metis.tp-tabs:hover:before {
        width: 118px;
    }
    @media (max-width: 499px) {
        .metis.tp-tabs:before {
            background: rgba(0, 0, 0, 0.75);
        }
    }
