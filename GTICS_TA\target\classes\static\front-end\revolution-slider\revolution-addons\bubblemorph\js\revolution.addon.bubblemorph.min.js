/* 
 * <AUTHOR> <<EMAIL>>
 * @link      http://www.themepunch.com/
 * @copyright 2018 ThemePunch
*/

;(function(){function E(){var b=r(this),a=b.data("bubbleaddon");a&&(a.destroy(),b.removeData("bubbleaddon"))}function n(b,a){if(!b)return!1;if(0===a)return b[a];for(var f=a,e=b[a];"inherit"===e;)f--,e=-1<f?b[f]:b[0];return e}function q(){var b=r(this);b.data("bubbleaddon")&&E.call(this);var a=b.data("bubbleObj");if(a){var f=a.levels,e=f.length,k=B.width(),c=0;if(f)for(var h=0;h<e;h++){var p=f[h];if(x!==p){k<p&&(c=h);var x=p}}f=a.layerW[c];e=a.layerH[c];e="100%"===e||"full"===e;"100%"!==f&&"full"!==
f?f=parseInt(a.layr.css("min-width"),10):(f=!a.carousel||a.isStatic?a.slider:a.slotholder,a.fullAlign?(f=f.width(),a.wrapper[0].style.left=0):(f=Math.min(a.slider.width()/a.grids[c],1),f*=a.grids[c]));e?(e=a.isStatic?a.slider.height():a.slotholder.height(),a.wrapper[0].style.top=0):e=parseInt(a.layr.css("min-height"),10);k=n(a.blurStrength,c);x=n(a.borderSize,c);p=n(a.borderColor,c);h=n(a.blurColor,c);var u=n(a.blurX,c),G=n(a.blurY,c),v=n(a.bufferX,c),l=n(a.bufferY,c),g=n(a.numBubbles,c),q=n(a.velX,
c);c=n(a.velY,c);a=I(f,e,a.slider,a.canvas,parseInt(g,10),a.color,parseInt(k,10),h,parseInt(u,10),parseInt(G,10),p,parseInt(x,10),parseInt(v,10),parseInt(l,10),parseFloat(q),parseFloat(c));b.data("bubbleaddon",a);b.data("bubblesplaying")&&C(a,b)}}function C(b,a){b.pause=!1;b.screen.resize();b.started||(b.started=!0,b.inited());a.data("bubblesplaying",!0);b.play()}function H(b,a){this.slider=b;this.carousel=a;b.one("revolution.slide.onloaded",this.onLoaded.bind(this)).one("rsBubbleMorphDestroyed",
this.destroy.bind(this))}function J(b){if("transparent"===b.trim())return["#FFFFFF",!1];if(-1!==b.search(/\[\{/))try{b=JSON.parse(b.replace(/&/g,'"'));for(var a=b.colors,f=a.length,e=[],k,c=0;c<f;c++){var h=a[c];delete h.align;k?JSON.stringify(h)!==JSON.stringify(k)&&(e[e.length]=h):e[e.length]=h;k=h}b.colors=e;return[b,!0]}catch(p){return["#FFFFFF",!1]}else return-1!==b.search("#")?[b,!1]:-1!==b.search("rgba")?[b.replace(/\s/g,"").replace(/false/g,"1"),!1]:-1!==b.search("rgb")?[b.replace(/\s/g,""),
!1]:/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(b)?[b,!1]:["#FFFFFF",!1]}function I(b,a,f,e,k,c,h,p,n,r,G,v,l,g,q,E){var w,x={screen:{elem:null,callback:null,ctx:null,width:0,height:0,left:0,top:0,init:function(d,a){this.elem=e;this.callback=d||null;"CANVAS"==this.elem.tagName&&(this.ctx=this.elem.getContext("2d"));this.elem.onselectstart=function(){return!1};this.elem.ondrag=function(){return!1};a&&this.resize();return this},resize:function(){var d=this.elem;this.width=b;this.height=a;for(this.top=
this.left=0;null!=d;d=d.offsetParent)this.left+=d.offsetLeft,this.top+=d.offsetTop;this.ctx&&(this.elem.width=this.width,this.elem.height=this.height);w&&(w.width=this.width,w.height=this.height);this.callback&&this.callback()},destroy:function(){for(var d in this)this.hasOwnProperty(d)&&delete this[d]}}},y=function(d,a){this.x=d;this.y=a;this.magnitude=d*d+a*a;this.force=this.computed=0};y.prototype.add=function(d){return new y(this.x+d.x,this.y+d.y)};var u=function(d){this.vel=new y((.5<Math.random()?
1:-1)*(.2+Math.random()*q),(.5<Math.random()?1:-1)*(.2+Math.random()*E));this.pos=new y(.2*d.width+Math.random()*d.width*.6,.2*d.height+Math.random()*d.height*.6);this.size=d.wh/15+d.wh/15*(1.4*Math.random()+.1);this.width=d.width;this.height=d.height};u.prototype.move=function(){this.pos.x>=this.width-this.size-l?(0<this.vel.x&&(this.vel.x=-this.vel.x),this.pos.x=this.width-this.size-l):this.pos.x<=this.size+l&&(0>this.vel.x&&(this.vel.x=-this.vel.x),this.pos.x=this.size+l);this.pos.y>=this.height-
this.size-g?(0<this.vel.y&&(this.vel.y=-this.vel.y),this.pos.y=this.height-this.size-g):this.pos.y<=this.size+g&&(0>this.vel.y&&(this.vel.y=-this.vel.y),this.pos.y=this.size+g);this.pos=this.pos.add(this.vel)};var A=function(d,a,b,e){this.step=5;this.width=d;this.height=a;this.wh=Math.min(d,a);this.sx=Math.floor(this.width/this.step);this.sy=Math.floor(this.height/this.step);this.paint=!1;this.metaFill=B(d,a,e);this.plx=[0,0,1,0,1,1,1,1,1,1,0,1,0,0,0,0];this.ply=[0,0,0,0,0,0,1,0,0,1,1,1,0,1,0,1];
this.mscases=[0,3,0,3,1,3,0,3,2,2,0,2,1,1,0];this.ix=[1,0,-1,0,0,1,0,-1,-1,0,1,0,0,1,1,0,0,0,1,1];this.grid=[];this.balls=[];this.iter=0;this.sign=1;for(d=0;d<(this.sx+2)*(this.sy+2);d++)this.grid[d]=new y(d%(this.sx+2)*this.step,Math.floor(d/(this.sx+2))*this.step);for(d=0;d<b;d++)this.balls[d]=new u(this)};A.prototype.destroy=function(){for(var d in this.balls)this.balls.hasOwnProperty(d)&&delete this.balls[d];for(d in this)this.hasOwnProperty(d)&&delete this[d]};A.prototype.computeForce=function(d,
a,b){b=b||d+a*(this.sx+2);if(0===d||0===a||d===this.sx||a===this.sy)d=.6*this.sign;else{d=0;a=this.grid[b];if(!a)return;for(var e=0,z;z=this.balls[e++];)d+=z.size*z.size/(-2*a.x*z.pos.x-2*a.y*z.pos.y+z.pos.magnitude+a.magnitude);d*=this.sign}if(this.grid[b])return this.grid[b].force=d};A.prototype.marchingSquares=function(a){var d=a[0],b=a[1],e=a[2];a=d+b*(this.sx+2);if(this.grid[a]){if(this.grid[a].computed===this.iter)return!1;for(var c,f=0,h=0;4>h;h++){var g=d+this.ix[h+12]+(b+this.ix[h+16])*(this.sx+
2);if(!this.grid[g]){c=!0;break}var k=this.grid[g].force;if(0<k&&0>this.sign||0>k&&0<this.sign||!k)k=this.computeForce(d+this.ix[h+12],b+this.ix[h+16],g);1<Math.abs(k)&&(f+=Math.pow(2,h))}if(!c){if(15===f)return[d,b-1,!1];if(5===f)c=2===e?3:1;else if(10===f)c=3===e?0:2;else{c=this.mscases[f];if(!this.grid[a])return;this.grid[a].computed=this.iter}a=this.step/(Math.abs(Math.abs(this.grid[d+this.plx[4*c+2]+(b+this.ply[4*c+2])*(this.sx+2)].force)-1)/Math.abs(Math.abs(this.grid[d+this.plx[4*c+3]+(b+this.ply[4*
c+3])*(this.sx+2)].force)-1)+1);m.lineTo(this.grid[d+this.plx[4*c]+(b+this.ply[4*c])*(this.sx+2)].x+this.ix[c]*a,this.grid[d+this.plx[4*c+1]+(b+this.ply[4*c+1])*(this.sx+2)].y+this.ix[c+4]*a);this.paint=!0;return[d+this.ix[c+4],b+this.ix[c+8],c]}}};A.prototype.renderMetaballs=function(){for(var a=0,b;b=this.balls[a++];)b.move();this.iter++;this.sign=-this.sign;this.paint=!1;m.fillStyle=this.metaFill;h&&(m.shadowBlur=h,m.shadowColor=p,m.shadowOffsetX=n,m.shadowOffsetY=r);v&&(m.strokeStyle=G,m.lineWidth=
v);m.beginPath();for(a=0;b=this.balls[a++];){b=[Math.round(b.pos.x/this.step),Math.round(b.pos.y/this.step),!1];do b=this.marchingSquares(b);while(b);this.paint&&(m.fill(),m.closePath(),v&&m.stroke(),m.beginPath(),this.paint=!1)}};var B=function(a,b,c){if(c[1]){c=c[0];if("radial"===c.type){var d=c.colors;var e=.5*a;a=.5*b;e=m.createRadialGradient(e,a,0,e,a,e);a=d.length;for(c=0;c<a;c++)b=d[c],e.addColorStop(.01*b.position,"rgba("+b.r+", "+b.g+", "+b.b+", "+b.a+")");return e}e=m;d=c.colors;c=c.angle;
var f=0,h=0,k=0,g=0;c=parseInt(c,10);if(/0|90|180|270|360/.test(c))switch(c){case 0:case 360:h=b;break;case 90:k=a;break;case 180:g=b;break;case 270:f=a}else a*=.5,b*=.5,g=Math.PI/180*(c-180),c=b/Math.cos(g),c+=Math.sin(g)*(a-Math.sqrt(c*c-b*b)),f=a+Math.cos(-Math.PI/2+g)*c,h=b+Math.sin(-Math.PI/2+g)*c,k=a+Math.cos(Math.PI/2+g)*c,g=b+Math.sin(Math.PI/2+g)*c;e=e.createLinearGradient(Math.round(f),Math.round(h),Math.round(k),Math.round(g));c=d.length;for(f=0;f<c;f++)a=d[f],b=parseInt(a.position,10),
e.addColorStop(.01*b,"rgba("+a.r+", "+a.g+", "+a.b+", "+a.a+")");return e}return c[0]},F=function(){D&&!D.pause&&(m.clearRect(0,0,t.width,t.height),w.renderMetaballs(),requestAnimationFrame(F))},t=x.screen.init(null,!0),m=t.ctx,C=function(){w=new A(t.width,t.height,k,c)},D={play:F,pause:!1,screen:t,inited:C,started:!1,destroy:function(){D.pause=!0;cancelAnimationFrame(F);m.clearRect(0,0,t.width,t.height);w&&w.destroy();t.destroy();m=B=C=D=t=F=A=u=y=x=w=null}};return D}var r,B;window.BubbleMorphAddOn=
function(b,a,f){if(b&&a)return r=b,B=r(window),r.event.special.rsBubbleMorphDestroyed={remove:function(a){a.handler()}},new H(a,f)};H.prototype={onLoaded:function(){var b,a=this.slider,f=this.carousel,e=a[0].opt.gridwidth,k=a[0].opt.responsiveLevels;for(Array.isArray(e)||(e=[e]);4>e.length;)e[e.length]=e[e.length-1];for(b=0;4>b;b++)e[b]=parseInt(e[b],10);if(k){for(Array.isArray(k)||(k=[k]);4>k.length;)k[k.length]=k[k.length-1];for(b=0;4>b;b++)k[b]=parseInt(k[b],10)}this.morph=a.find(".tp-bubblemorph").each(function(){var b=
r(this),h=r("<canvas />").appendTo(b),p=this.getAttribute("data-numbubbles"),n=this.getAttribute("data-bubblesspeedx"),u=this.getAttribute("data-bubblesspeedy"),q=this.getAttribute("data-bubblesbufferx"),v=this.getAttribute("data-bubblesbuffery"),l=b.attr("data-width").replace(/[[\]]/g,"").replace(/'/g,"").split(","),g=b.attr("data-height").replace(/[[\]]/g,"").replace(/'/g,"").split(",");Array.isArray(l)||(l=[l]);for(Array.isArray(g)||(g=[g]);4>l.length;)l[l.length]=l[l.length-1];for(;4>g.length;)g[g.length]=
g[g.length-1];for(;g.length<l.length;)g[g.length]=g[g.length-1];for(;l.length<g.length;)g[l.length]=l[l.length-1];p=p.split("|");q=q.split("|");v=v.split("|");n=n.split("|");u=u.split("|");h={velX:n,velY:u,layr:b,grids:e,layerW:l,layerH:g,slider:a,levels:k,bufferX:q,bufferY:v,canvas:h[0],carousel:f,numBubbles:p,wrapper:b.closest(".tp-parallax-wrap"),isStatic:b.hasClass("tp-static-layer"),color:J(this.getAttribute("data-bubblesbg")),fullAlign:"slide"===this.getAttribute("data-basealign"),slotholder:b.closest(".tp-revslider-slidesli").find(".slotholder")};
if(p=this.getAttribute("data-bubblesblur"))h.blurStrength=p.split("|"),h.blurColor=b.attr("data-bubblesblurcolor").split("|"),h.blurX=b.attr("data-bubblesblurx").split("|"),h.blurY=b.attr("data-bubblesblury").split("|");if(p=this.getAttribute("data-bubblesbordersize"))h.borderSize=p.split("|"),h.borderColor=b.attr("data-bubblesbordercolor").split("|");b.data("bubbleObj",h)});if(this.morph.length)a.on("revolution.slide.afterdraw",this.onResize.bind(this)).on("revolution.slide.layeraction",this.layerAction.bind(this));
else this.destroy()},createBubbles:function(){this.morph.each(q)},onResize:function(b){clearTimeout(this.resizeTimer);this.morph.each(E);this.resizeTimer=setTimeout(this.resize.bind(this),250)},resize:function(){this.morph.each(q)},layerAction:function(b,a){var f=a.layer.data("bubbleaddon");if(!f)if(a.layer.hasClass("tp-bubblemorph"))this.createBubbles();else return;f=a.layer.data("bubbleaddon");f.screen&&f.screen.width&&f.screen.height||(q.call(a.layer),f=a.layer.data("bubbleaddon"));switch(a.eventtype){case "enterstage":C(f,
a.layer);break;case "leftstage":var e=a.layer;f.pause=!0;e.data("bubblesplaying",!1)}},checkRemoved:function(){return this.slider&&document.body.contains(this.slider[0])?!1:(this.destroy(),!0)},destroy:function(){this.slider.find(".tp-bubblemorph").each(function(){var a=r(this);a.data("bubbleaddon").pause=!0;a.removeData("bubbleaddon bubbleObj")});for(var b in this)this.hasOwnProperty(b)&&delete this[b]}}})();