<!--  --><!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Admin - Agregar <PERSON></title>

  <!-- Vendors Style-->
  <link rel="stylesheet" th:href="@{/css/vendors_css.css}">
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <link rel="stylesheet" th:href="@{/css/skin_color.css}">

  <!-- Google Maps API - REEMPLAZAR CON TU PROPIA API KEY -->
  <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBDaeWicvigtP9xPv919E-RNoxfvC-Hqik&libraries=places&callback=initMap"></script>

  <!-- Custom CSS para wizard -->
  <style>
    .service-type-btn {
      height: 60px;
      transition: all 0.3s ease;
      border: 2px solid #6c757d;
      margin-bottom: 15px; 
      font-weight: 500;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 1.2;
      padding: 8px 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      hyphens: auto;
      border-radius: 6px;
    }

    .service-type-btn:last-child {
      margin-right: 0;
    }

    .service-type-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      border-color: #008B9A;
      color: #008B9A;
    }

    .service-type-btn.active {
      background-color: #008B9A !important;
      color: white !important;
      border-color: #008B9A !important;
      box-shadow: 0 4px 12px rgba(0, 139, 154, 0.3);
    }

    .service-type-btn.active:hover {
      background-color: #007a87 !important;
      border-color: #007a87 !important;
      transform: translateY(-1px);
    }

    /* Responsive text adjustments */
    @media (max-width: 768px) {
      .service-type-btn {
        font-size: 12px;
        height: 50px;
        padding: 5px 8px;
      }
    }

    @media (max-width: 576px) {
      .service-type-btn {
        font-size: 11px;
        height: 45px;
        padding: 4px 6px;
      }
    }

    .service-fields {
      animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .wizard-content .steps ul li {
      width: 50%;
    }

    .wizard-content .steps ul li.current .step {
      background-color: #008B9A;
      border-color: #008B9A;
    }

    .wizard-content .steps ul li.current .step .number {
      color: white;
    }

    .wizard-content .steps ul li.done .step {
      background-color: #28a745;
      border-color: #28a745;
    }

    .wizard-content .steps ul li.done .step .number {
      color: white;
    }

  </style>
</head>
<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
  <div id="loader"></div>

  <header class="main-header">
    <div class="d-flex align-items-center logo-box justify-content-center">
      <a th:href="@{/admin}" class="logo">
        <div class="logo-mini w-150 text-center">
          <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
        </div>
      </a>
    </div>
    <nav class="navbar navbar-static-top">
      <div class="app-menu">
        <ul class="header-megamenu nav">
          <li class="btn-group nav-item">
            <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
              <i data-feather="align-left"></i>
            </a>
          </li>
        </ul>
      </div>
      <div th:replace="~{fragments :: navbarAdmin}"></div>
    </nav>
  </header>

  <aside th:replace="~{fragments :: sideBarAdmin}"></aside>

  <div class="content-wrapper">
    <div class="container-full">
      <div class="content-header">
        <div class="d-flex align-items-center">
          <div class="me-auto">
            <h4 class="page-title">Agregar Servicio</h4>
          </div>
        </div>
      </div>
    </div>

    <section class="content">
      <div class="box">
        <div class="box-header with-border">
          <h4 class="box-title">Nuevo Servicio</h4>
          <h6 class="box-subtitle">Ingrese los datos necesarios para crear el servicio</h6>
        </div>
        
        <div class="box-body wizard-content">
          <form th:action="@{/admin/guardarservicio}" th:object="${servicioDTO}" method="post" enctype="multipart/form-data" class="validation-wizard wizard-circle">

            <!-- Step 1: Información Básica -->
            <h6>Información Básica</h6>
            <section>
              <!-- Información básica -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="nombreLugar" class="form-label">Nombre del lugar: <span class="text-danger">*</span></label>
                    <input type="text" th:field="*{espacio.nombre}" class="form-control required" id="nombreLugar" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="ubicacionInput" class="form-label">Ubicación: <span class="text-danger">*</span></label>
                    <div class="input-group">
                      <input type="text" th:field="*{espacio.ubicacion}" class="form-control required" id="ubicacionInput"
                             placeholder="Ingrese la dirección en San Miguel, Lima" required>
                      <button type="button" class="btn btn-primary" id="geocodificarBtn" onclick="geocodificarDireccion()">
                        <i class="fa fa-map-marker"></i> Geocodificar
                      </button>
                    </div>
                    <small class="form-text text-muted">Ingrese la dirección y presione "Geocodificar" para verificar en el mapa</small>
                  </div>
                </div>
              </div>

            <!-- Mapa de Google Maps -->
            <div class="row" id="mapaContainer" style="display: none;">
              <div class="col-md-12">
                <div class="form-group">
                  <label class="form-label">Verificar ubicación en el mapa:</label>
                  <div id="mapInfo" class="alert alert-info" style="display: none; margin-bottom: 10px;">
                    <small id="mapServiceInfo"></small>
                  </div>
                  <div id="map" style="height: 400px; width: 100%; border: 1px solid #ddd; border-radius: 5px;"></div>
                  <small class="form-text text-muted">Arrastre el marcador para ajustar la ubicación exacta</small>
                </div>
              </div>
            </div>

              <!-- Horarios -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="horaAbre" class="form-label">Hora de apertura: <span class="text-danger">*</span></label>
                    <input th:field="*{espacio.horaAbre}" type="time" class="form-control required" id="horaAbre" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="horaCierra" class="form-label">Hora de cierre: <span class="text-danger">*</span></label>
                    <input th:field="*{espacio.horaCierra}" type="time" class="form-control required" id="horaCierra" required>
                  </div>
                </div>
              </div>

              <!-- Información de contacto -->
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="correoContacto" class="form-label">Correo contacto: <span class="text-danger">*</span></label>
                    <input type="email" th:field="*{espacio.correoContacto}" class="form-control required" id="correoContacto" required>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="numContacto" class="form-label">Teléfono contacto: <span class="text-danger">*</span></label>
                    <input type="tel" th:field="*{espacio.numContacto}" class="form-control required" id="numContacto" required>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="costoHorario" class="form-label">Costo por hora (S/.): <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" th:field="*{espacio.costoHorario}" class="form-control required" id="costoHorario" required>
                  </div>
                </div>
              </div>

              <!-- Información adicional del espacio -->
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="aforo" class="form-label">Aforo: <span class="text-danger">*</span></label>
                    <input type="number" class="form-control required" th:field="*{espacio.aforo}" id="aforo" min="1" required>
                    <small class="form-text text-muted">Número máximo de personas</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="descripcionCorta" class="form-label">Descripción: <span class="text-danger">*</span></label>
                    <input type="text" class="form-control required" th:field="*{espacio.descripcionCorta}" id="descripcionCorta" maxlength="100" required>
                    <small class="form-text text-muted">Descripción breve (máx. 100 caracteres)</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="descripcionLarga" class="form-label">Detalles:</label>
                    <textarea class="form-control" th:field="*{espacio.descripcionLarga}" id="descripcionLarga" rows="3" maxlength="500" placeholder="Descripción detallada del espacio deportivo"></textarea>
                    <small class="form-text text-muted">Descripción detallada (máx. 500 caracteres)</small>
                  </div>
                </div>
              </div>

              <!-- Fotos -->
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label for="archivos" class="form-label">Fotos del lugar: <span class="text-danger">*</span></label>
                    <input class="form-control required" type="file" id="archivos" name="archivos" accept="image/*" multiple required>
                    <small class="form-text text-muted">Seleccione hasta 4 imágenes</small>
                  </div>
                </div>
              </div>
            </section>

            <!-- Step 2: Tipo de Servicio -->
            <h6>Tipo de Servicio</h6>
            <section>
              <!-- Selector de tipo de servicio con botones -->
              <div class="row mb-4">
                <div class="col-md-12">
                  <h5 class="mb-3">Seleccione el tipo de servicio deportivo:</h5>
                  <div class="row">
                    <!-- Piscina -->
                    <div class="col-md-2 col-sm-6 mb-3 d-flex justify-content-center">
                      <button type="button" class="btn btn-outline-secondary btn-lg text-center btn-custom-padding service-type-btn"
                              data-type="piscina" data-id="1" onclick="seleccionarTipoServicio('piscina', 1)">
                        Piscina
                      </button>
                    </div>

                    <!-- Cancha Fútbol -->
                    <div class="col-md-4 col-sm-6 mb-3 d-flex justify-content-center">
                      <button type="button" class="btn btn-outline-secondary btn-lg text-center btn-custom-padding service-type-btn"
                              data-type="cancha" data-id="2" onclick="seleccionarTipoServicio('cancha', 2)">
                        Cancha Fútbol
                      </button>
                    </div>

                    <!-- Pista Atletismo -->
                    <div class="col-md-4 col-sm-6 mb-3 d-flex justify-content-center">
                      <button type="button" class="btn btn-outline-secondary btn-lg text-center btn-custom-padding service-type-btn"
                              data-type="pista" data-id="3" onclick="seleccionarTipoServicio('pista', 3)">
                        Pista Atletismo
                      </button>
                    </div>

                    <!-- Estadio -->
                    <div class="col-md-2 col-sm-6 mb-3 d-flex justify-content-center">
                      <button type="button" class="btn btn-outline-secondary btn-lg text-center btn-custom-padding service-type-btn"
                              data-type="estadio" data-id="4" onclick="seleccionarTipoServicio('estadio', 4)">
                        Estadio
                      </button>
                    </div>
                  </div>
                </div>
              </div>




              <!-- Campos ocultos -->
              <input type="hidden" th:field="*{espacio.tipoEspacio.id}" id="tipoEspacioId" value="1">
              <input type="hidden" th:field="*{espacio.latitud}" id="latitud">
              <input type="hidden" th:field="*{espacio.longitud}" id="longitud">
              <input type="hidden" th:field="*{espacio.mapsUrl}" id="mapsUrl">

              <!-- Campos específicos dinámicos -->
              <div id="serviceSpecificFields">

                <!-- Campos específicos para PISCINA -->
                <div id="campoPiscina" class="service-fields" style="display: block;">
                  <h5 class="mb-3" style="color: #008B9A;">
                    <i class="fa fa-swimmer me-2"></i>🏊‍♀️ Configuración de Piscina
                  </h5>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="tipoPiscina" class="form-label">Tipo de Piscina:</label>
                        <select th:field="*{piscina.tipoPiscina}" class="form-select" id="tipoPiscina">
                          <option value="">Seleccione tipo</option>
                          <option value="Olimpica">Olímpica</option>
                          <option value="Publica">Pública</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="profundidadMin" class="form-label">Profundidad Mín. (m):</label>
                        <input type="number" step="0.01" th:field="*{piscina.profundidadMin}" class="form-control" id="profundidadMin" placeholder="1.20">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="profundidadMax" class="form-label">Profundidad Máx. (m):</label>
                        <input type="number" step="0.01" th:field="*{piscina.profundidadMax}" class="form-control" id="profundidadMax" placeholder="2.00">
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label class="form-label">¿Climatizada?</label>
                        <div class="form-check-inline">
                          <input type="radio" th:field="*{piscina.climatizada}" value="true" id="climatizadaSi" class="form-check-input">
                          <label for="climatizadaSi" class="form-check-label me-3">Sí</label>
                          <input type="radio" th:field="*{piscina.climatizada}" value="false" id="climatizadaNo" class="form-check-input">
                          <label for="climatizadaNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="numCarrilMax" class="form-label">Número de Carriles:</label>
                        <input type="number" th:field="*{piscina.numCarrilMax}" class="form-control" id="numCarrilMax"
                               placeholder="Ej: 8" min="1" max="20">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="requisitos" class="form-label">Requisitos:</label>
                        <input type="text" th:field="*{piscina.requisitos}" class="form-control" id="requisitos"
                               placeholder="Ej: Gorro de baño obligatorio" maxlength="255">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Campos específicos para CANCHA DE FÚTBOL -->
                <div id="campoCancha" class="service-fields" style="display: none;">
                  <h5 class="mb-3" style="color: #008B9A;">
                    <i class="fa fa-futbol me-2"></i>⚽ Configuración de Cancha de Fútbol
                  </h5>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="tipoSuperficie" class="form-label">Superficie:</label>
                        <select th:field="*{cancha.tipoSuperficie}" class="form-select" id="tipoSuperficie">
                          <option value="">Seleccione superficie</option>
                          <option value="Grass">Grass</option>
                          <option value="Losa">Losa</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="ancho" class="form-label">Ancho (metros):</label>
                        <input type="number" step="0.01" th:field="*{cancha.ancho}" class="form-control" id="ancho" placeholder="68">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="alto" class="form-label">Largo (metros):</label>
                        <input type="number" step="0.01" th:field="*{cancha.alto}" class="form-control" id="alto" placeholder="105">
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">¿Iluminación Nocturna?</label>
                        <div class="form-check-inline">
                          <input type="radio" th:field="*{cancha.iluminacionNocturna}" value="true" id="iluminacionSi" class="form-check-input">
                          <label for="iluminacionSi" class="form-check-label me-3">Sí</label>
                          <input type="radio" th:field="*{cancha.iluminacionNocturna}" value="false" id="iluminacionNo" class="form-check-input">
                          <label for="iluminacionNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">¿Balones Disponibles?</label>
                        <div class="form-check-inline">
                          <input type="radio" th:field="*{cancha.balonesDisponibles}" value="true" id="balonesSi" class="form-check-input">
                          <label for="balonesSi" class="form-check-label me-3">⚽ Sí</label>
                          <input type="radio" th:field="*{cancha.balonesDisponibles}" value="false" id="balonesNo" class="form-check-input">
                          <label for="balonesNo" class="form-check-label">❌ No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Campos específicos para PISTA DE ATLETISMO -->
                <div id="campoPista" class="service-fields" style="display: none;">
                  <h5 class="mb-3" style="color: #008B9A;">
                    <i class="fa fa-running me-2"></i>🏃‍♂️ Configuración de Pista de Atletismo
                  </h5>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="tipoSuperficiePista" class="form-label">Tipo de Superficie:</label>
                        <select th:field="*{pista.tipoSuperficie}" class="form-select" id="tipoSuperficiePista">
                          <option value="">Seleccione superficie</option>
                          <option value="Tartan">Tartan</option>
                          <option value="Asfalto">Asfalto</option>
                          <option value="Tierra">Tierra</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="longitud" class="form-label">Longitud (metros):</label>
                        <input type="number" step="0.01" th:field="*{pista.longitud}" class="form-control" id="longitud" placeholder="400">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="implementos" class="form-label">Implementos Disponibles:</label>
                        <input type="text" th:field="*{pista.implementos}" class="form-control" id="implementos" placeholder="Ej: Vallas, postes, cronómetros">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Campos específicos para ESTADIO -->
                <div id="campoEstadio" class="service-fields" style="display: none;">
                  <h5 class="mb-3" style="color: #008B9A;">
                    <i class="fa fa-building me-2"></i>🏟️ Configuración de Estadio
                  </h5>
                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-group">
                        <label for="usoPermitido" class="form-label">Uso Permitido:</label>
                        <textarea th:field="*{estadios.usoPermitido}" class="form-control" id="usoPermitido" rows="3"
                                  placeholder="Describa los usos permitidos del estadio (eventos deportivos, conciertos, etc.)"></textarea>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label class="form-label">¿Seguridad Disponible?</label>
                        <div class="form-check-inline">
                          <input type="radio" th:field="*{estadios.seguridadDisponible}" value="true" id="seguridadSi" class="form-check-input">
                          <label for="seguridadSi" class="form-check-label me-3">Sí</label>
                          <input type="radio" th:field="*{estadios.seguridadDisponible}" value="false" id="seguridadNo" class="form-check-input">
                          <label for="seguridadNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label class="form-label">¿Sonido y Pantallas?</label>
                        <div class="form-check-inline">
                          <input type="radio" th:field="*{estadios.sonidoPantallasDisponible}" value="true" id="sonidoSi" class="form-check-input">
                          <label for="sonidoSi" class="form-check-label me-3">Sí</label>
                          <input type="radio" th:field="*{estadios.sonidoPantallasDisponible}" value="false" id="sonidoNo" class="form-check-input">
                          <label for="sonidoNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label class="form-label">¿Iluminación Profesional?</label>
                        <div class="form-check-inline">
                          <input type="radio" th:field="*{estadios.iluminacionProfesionalDisponible}" value="true" id="iluminacionProfSi" class="form-check-input">
                          <label for="iluminacionProfSi" class="form-check-label me-3">Sí</label>
                          <input type="radio" th:field="*{estadios.iluminacionProfesionalDisponible}" value="false" id="iluminacionProfNo" class="form-check-input">
                          <label for="iluminacionProfNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>



          </form>
        </div>
      </div>
    </section>
  </div>

  <footer class="main-footer">
    &copy; <script>document.write(new Date().getFullYear())</script> 
    <a href="https://munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. 
    Todos los derechos reservados.
  </footer>

</div>

<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script th:src="@{/js/template.js}"></script>

<!-- jQuery Steps Plugin -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-steps/1.1.0/jquery.steps.min.js"></script>

<script>
  // Variables globales para Google Maps
  let map;
  let marker;
  let geocoder;
  let autocomplete;

  // Inicializar Google Maps (callback desde la API)
  function initMap() {
    try {
      // Verificar que Google Maps esté disponible
      if (!window.google || !window.google.maps) {
        throw new Error('Google Maps API no está disponible');
      }

      // Coordenadas de San Miguel, Lima, Perú
      const sanMiguelCenter = { lat: -12.0776, lng: -77.0865 };

      // Crear el mapa
      map = new google.maps.Map(document.getElementById('map'), {
        zoom: 15,
        center: sanMiguelCenter,
        mapTypeId: 'roadmap',
        streetViewControl: false,
        mapTypeControl: true,
        fullscreenControl: true
      });

      // Inicializar geocoder
      geocoder = new google.maps.Geocoder();

      // Crear marcador arrastrable
      marker = new google.maps.Marker({
        position: sanMiguelCenter,
        map: map,
        draggable: true,
        title: 'Arrastre para ajustar la ubicación',
        animation: google.maps.Animation.DROP
      });

      // Configurar autocomplete para el input de ubicación
      const ubicacionInput = document.getElementById('ubicacionInput');
      if (ubicacionInput && google.maps.places) {
        autocomplete = new google.maps.places.Autocomplete(ubicacionInput, {
          bounds: new google.maps.LatLngBounds(
            new google.maps.LatLng(-12.1200, -77.1200), // SW - área más amplia
            new google.maps.LatLng(-12.0400, -77.0400)  // NE
          ),
          strictBounds: false,
          componentRestrictions: { country: 'pe' },
          types: ['address'],
          fields: ['geometry', 'formatted_address', 'name']
        });

        // Listener para cuando se selecciona una dirección del autocomplete
        autocomplete.addListener('place_changed', function() {
          const place = autocomplete.getPlace();
          if (place.geometry && place.geometry.location) {
            actualizarMapa(place.geometry.location);
            document.getElementById('mapaContainer').style.display = 'block';
          }
        });
      }

      // Listener para cuando se arrastra el marcador
      marker.addListener('dragend', function() {
        const position = marker.getPosition();
        actualizarCoordenadas(position.lat(), position.lng());
        actualizarMapsUrl(position.lat(), position.lng());

        // Geocodificación inversa para actualizar la dirección
        geocoder.geocode({ location: position }, function(results, status) {
          if (status === 'OK' && results[0]) {
            document.getElementById('ubicacionInput').value = results[0].formatted_address;
          }
        });
      });

      mostrarInfoServicioMapa('✅ Google Maps cargado correctamente');
      console.log('Google Maps inicializado correctamente');

    } catch (error) {
      console.error('Error al inicializar Google Maps:', error);
      mostrarInfoServicioMapa('❌ Error al cargar Google Maps - Verifique la API key');
      mostrarMensajeApiKey();
    }
  }

  // Mostrar mensaje sobre API key
  function mostrarMensajeApiKey() {
    const mapContainer = document.getElementById('map');
    mapContainer.innerHTML = `
      <div style="padding: 20px; text-align: center; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
        <h5 style="color: #dc3545; margin-bottom: 15px;">⚠️ Google Maps no disponible</h5>
        <p style="margin-bottom: 10px;"><strong>Para usar Google Maps necesitas tu propia API key:</strong></p>
        <ol style="text-align: left; display: inline-block; margin-bottom: 15px;">
          <li>Ve a <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
          <li>Crea un proyecto nuevo</li>
          <li>Habilita: Maps JavaScript API, Places API, Geocoding API</li>
          <li>Crea una API key en "Credenciales"</li>
          <li>Reemplaza la clave en el código</li>
        </ol>
        <p style="color: #6c757d; font-size: 0.9em;">Google ofrece $200 USD gratis mensualmente - suficiente para desarrollo</p>
      </div>
    `;
  }

  // Manejar errores de carga de Google Maps
  window.gm_authFailure = function() {
    console.error('Error de autenticación de Google Maps');
    mostrarInfoServicioMapa('❌ API key inválida o expirada');
    mostrarMensajeApiKey();
  };

  // Mostrar información sobre el servicio de mapas
  function mostrarInfoServicioMapa(mensaje) {
    const mapInfo = document.getElementById('mapInfo');
    const mapServiceInfo = document.getElementById('mapServiceInfo');
    if (mapInfo && mapServiceInfo) {
      mapServiceInfo.textContent = mensaje;
      mapInfo.style.display = 'block';

      // Ocultar después de 3 segundos
      setTimeout(() => {
        mapInfo.style.display = 'none';
      }, 3000);
    }
  }

  // Función para geocodificar la dirección ingresada
  function geocodificarDireccion() {
    const direccion = document.getElementById('ubicacionInput').value.trim();
    if (!direccion) {
      alert('Por favor, ingrese una dirección');
      return;
    }

    // Verificar que Google Maps esté disponible
    if (!window.google || !window.google.maps || !geocoder) {
      alert('Google Maps no está disponible. Por favor, recargue la página.');
      return;
    }

    // Deshabilitar botón mientras se procesa
    const btn = document.getElementById('geocodificarBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Buscando...';

    // Agregar contexto de ubicación si no está incluido
    let direccionCompleta = direccion;
    if (!direccion.toLowerCase().includes('san miguel') && !direccion.toLowerCase().includes('lima')) {
      direccionCompleta += ', San Miguel, Lima, Perú';
    } else if (!direccion.toLowerCase().includes('lima')) {
      direccionCompleta += ', Lima, Perú';
    }

    // Geocodificar con Google Maps
    geocoder.geocode({
      address: direccionCompleta,
      region: 'PE',
      bounds: new google.maps.LatLngBounds(
        new google.maps.LatLng(-12.1200, -77.1200), // SW - área más amplia
        new google.maps.LatLng(-12.0400, -77.0400)  // NE
      )
    }, function(results, status) {
      // Rehabilitar botón
      btn.disabled = false;
      btn.innerHTML = '<i class="fa fa-map-marker"></i> Geocodificar';

      if (status === 'OK' && results.length > 0) {
        const location = results[0].geometry.location;
        actualizarMapa(location);
        document.getElementById('mapaContainer').style.display = 'block';
        mostrarInfoServicioMapa('✅ Ubicación encontrada');
      } else {
        console.warn('Geocodificación falló:', status);
        mostrarInfoServicioMapa('❌ No se pudo encontrar la dirección');
        alert('No se pudo encontrar la dirección. Verifique que esté en San Miguel, Lima.');
      }
    });
  }

  // Actualizar la posición del mapa y marcador
  function actualizarMapa(location) {
    map.setCenter(location);
    marker.setPosition(location);
    actualizarCoordenadas(location.lat(), location.lng());
    actualizarMapsUrl(location.lat(), location.lng());
  }

  // Actualizar los campos ocultos de coordenadas
  function actualizarCoordenadas(lat, lng) {
    document.getElementById('latitud').value = lat.toFixed(8);
    document.getElementById('longitud').value = lng.toFixed(8);
  }

  // Actualizar el campo mapsUrl
  function actualizarMapsUrl(lat, lng) {
    const mapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
    document.getElementById('mapsUrl').value = mapsUrl;
  }









  // Función para seleccionar tipo de servicio
  function seleccionarTipoServicio(tipo, id) {
    // Actualizar campo oculto
    document.getElementById('tipoEspacioId').value = id;

    // Actualizar botones
    document.querySelectorAll('.service-type-btn').forEach(btn => {
      btn.classList.remove('active');
    });

    // Activar botón seleccionado
    const selectedBtn = document.querySelector(`[data-type="${tipo}"]`);
    if (selectedBtn) {
      selectedBtn.classList.add('active');
    }

    // Ocultar todos los campos específicos
    document.querySelectorAll('.service-fields').forEach(field => {
      field.style.display = 'none';
    });

    // Mostrar campos específicos del tipo seleccionado
    const targetField = document.getElementById(`campo${tipo.charAt(0).toUpperCase() + tipo.slice(1)}`);
    if (targetField) {
      targetField.style.display = 'block';
    }

    console.log(`Tipo de servicio seleccionado: ${tipo} (ID: ${id})`);
  }

  // Inicializar wizard
  function initializeWizard() {
    $(".validation-wizard").steps({
      headerTag: "h6",
      bodyTag: "section",
      transitionEffect: "fade",
      titleTemplate: '<span class="step">#index#</span> #title#',
      labels: {
        finish: "Guardar Servicio",
        next: "Siguiente",
        previous: "Anterior"
      },
      onStepChanging: function (event, currentIndex, newIndex) {
        // Validar step 1
        if (currentIndex === 0) {
          let isValid = true;
          $('.required').each(function() {
            if (!$(this).val()) {
              $(this).addClass('is-invalid');
              isValid = false;
            } else {
              $(this).removeClass('is-invalid');
            }
          });
          return isValid;
        }
        return true;
      },
      onStepChanged: function (event, currentIndex, priorIndex) {
        // Si llegamos al step 2, seleccionar piscina por defecto
        if (currentIndex === 1 && priorIndex === 0) {
          setTimeout(() => {
            seleccionarTipoServicio('piscina', 1);
          }, 100);
        }
      },
      onFinishing: function (event, currentIndex) {
        // Validar que se haya seleccionado un tipo de servicio
        const tipoEspacioId = document.getElementById('tipoEspacioId').value;
        if (!tipoEspacioId || tipoEspacioId === '') {
          alert('Por favor, seleccione un tipo de servicio');
          return false;
        }
        return true;
      },
      onFinished: function (event, currentIndex) {
        // Enviar formulario
        document.querySelector('form').submit();
      }
    });
  }

  document.addEventListener('DOMContentLoaded', function() {
    console.log('Página debug cargada correctamente');

    // Inicializar wizard
    initializeWizard();

  });

  feather.replace();
</script>

</body>
</html>
