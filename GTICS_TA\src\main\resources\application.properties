spring.application.name=GTICS_TA
spring.datasource.url=*********************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.open-in-view=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/

# Configuración de Google Maps
# Para desarrollo: usar la API key de prueba
# Para producción: la empresa debe reemplazar con su API key
google.maps.api.key=${GOOGLE_MAPS_API_KEY:AIzaSyB8gglF9Vdl-dlam3umZipOmq92nPlCEng}
google.maps.enabled=${GOOGLE_MAPS_ENABLED:true}
spring.thymeleaf.check-template-location=true
spring.thymeleaf.enabled=true
spring.thymeleaf.template-resolver-order=1
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=cgxu gnnj hhus tuhy
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Configuración de AWS S3
# Credenciales de AWS Academy
aws.s3.bucket.name=gtics-images-bucket-2025
aws.s3.region=us-east-1
aws.access.key.id=********************
aws.secret.access.key=pJh1G7EeLhsVyE7LgZmtKQQUAWUUQZD+Xn9m9RyW
aws.session.token=IQoJb3JpZ2luX2VjEPD//////////wEaCXVzLXdlc3QtMiJHMEUCIBngYN168/Xdj93RQLHfmpFyFbyIt3qAhyPN+1tIjC8LAiEA/cMYOs+VsScuqTCxhibLxO7wPrp9aVHtssUZnkUE+goqsQIIyf//////////ARAAGgw3MzAzMzU1MTMxODciDFqeM8lNPKpIbDkEmiqFAqcLxOeOTSjCS1Uq+2sH2zHUbm0JdVFRMoL0tT87DtaFEyT5T0ckOa1JpfWT4dhmgGqZmuJxIwG6ZKczBTHDZk8OKfROLnpAfKX05Nv+CWxqo3LM3UPaFkNRTJdFXgL9emc5geg9ON1hK2zi56IG/CI+Nbgc0jUV/gpKmpFYT49itNdWGjFM6a1ci4/uitZ8c9oQAELcq9KPZRMleazrowgYNH5b/9jCKNJnVeQktIHPPUPqWP7l+WXptCAqb5wecVrsMK5pSjKIktQm5IndPwQLV+FMwWbsCGJf86PKm06uW0hMBCNmcUSrLJAmcYWw2ZfMNP7MPrIWwBSsMWjAC5ze8uiAMTCDiaPCBjqdAbg0dPm2t2m1JoDCR5UX0cTud8hOY2a10A1BJzhRok44f9HdzZvgplos8apahJ5svz3mMp0DW892+OXJQQlEdXc5KXOjCpyS88UKeaTnRc+yvqvg60k9+mXGpW5ruP+d/lfxcFvxuJAF9hEYkFe+CGeFjwx3bF+Gokkrg1jBYBWJaObR1xlLPSy1Re1Lq6ZmQmc/AjIA0cI07wcRlyQ=
aws.s3.url.expiration.minutes=60

# Configuración de logging para debug
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web=DEBUG
logging.level.org.springframework.security.authentication=DEBUG
logging.level.com.example.gtics_ta=DEBUG
logging.level.org.springframework.jdbc=DEBUG
